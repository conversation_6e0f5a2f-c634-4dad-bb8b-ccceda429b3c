# VeryFL-PoL 快速开始指南

## 🚀 超简单使用方式

### 1️⃣ 最简单的开始
```bash
python run.py
```
然后跟着提示选择就行！系统会问你想做什么，选择对应的数字即可。

## 🔬 VeryFL-PoL 实验框架

### 📊 五个实验板块

#### 📊 实验1：基础性能对比（60分钟）
```bash
python run.py experiment1
```
对比PoL与传统FL在多个数据集上的性能表现。

#### 🛡️ 实验2：攻击防御效果（120分钟）
```bash
python run.py experiment2
```
验证PoL对各种攻击类型的防御能力。

#### 🗜️ 实验3：压缩效果分析（90分钟）
```bash
python run.py experiment3
```
分析PoL证明的压缩效果和存储开销。

#### 📈 实验4：可扩展性测试（120分钟）
```bash
python run.py experiment4
```
测试PoL在不同网络规模下的可扩展性。

#### 🧪 实验5：消融实验（90分钟）
```bash
python run.py experiment5
```
分析PoL各个组件的贡献度。

#### 🎯 完整五个板块（8小时）
```bash
python run.py all
```
运行完整的实验框架，获得所有数据。

## 🔄 增量式实验机制

### 📁 结果存储
每次运行实验都会保存到独立的时间戳目录：
```
experiments/results/
├── experiment1/
│   ├── 20250801_090530/  # 第一次运行
│   ├── 20250801_143022/  # 第二次运行
│   └── 20250802_101245/  # 第三次运行
├── experiment2/
│   ├── 20250801_100000/
│   └── 20250801_150000/
└── ...
```

### 📊 结果分析
使用专门的分析工具处理多次实验结果：

#### 查看可用实验
```bash
python analyze_results.py --list
```

#### 分析特定实验
```bash
python analyze_results.py --experiment experiment1
```

#### 分析指定时间段
```bash
python analyze_results.py --experiment experiment1 --timestamps 20250801_090530 20250801_143022
```

#### 生成图表
```bash
python analyze_results.py --experiment experiment1 --plots
```

### 📈 分析功能
- **统计分析**：均值、标准差、变异系数、置信区间
- **重复性验证**：多次实验结果的一致性检查
- **趋势分析**：时间序列上的性能变化
- **图表生成**：准确率曲线、箱线图、分布图
- **报告生成**：Markdown格式的详细分析报告

## 📊 批量实验（适合研究）

### 运行预定义实验套件
```bash
# 快速测试套件
python experiments.py suite --name quick

# 性能对比套件
python experiments.py suite --name performance

# 攻击防御套件
python experiments.py suite --name attack_defense

# 压缩效果套件
python experiments.py suite --name compression
```

### 网络可扩展性测试
```bash
# 测试不同客户端数量
python experiments.py network --clients 5,10,20,30
```

### 压缩方法对比
```bash
# 测试所有压缩方法
python experiments.py compression --methods all

# 测试特定方法
python experiments.py compression --methods none,quantization
```

## 🎯 使用建议

### 🔰 新手推荐路径
1. **先运行快速测试**：`python run.py demo`
2. **然后对比实验**：`python run.py compare`
3. **最后攻击测试**：`python run.py attack`

### 🔬 研究者推荐路径
1. **快速验证**：`python run.py demo`
2. **完整性能测试**：`python experiments.py suite --name performance`
3. **攻击防御测试**：`python experiments.py suite --name attack_defense`
4. **可扩展性测试**：`python experiments.py network --clients 10,20,50`

### 📚 论文实验
```bash
python run.py paper
```
运行论文中的所有关键实验（需要1-2小时）。

## 📋 实验结果

### 结果保存位置
- 实验结果：`results/` 目录
- 日志文件：控制台输出
- PoL证明：内存中（可配置保存到磁盘）

### 结果解读
- **准确率**：模型在测试集上的准确率
- **PoL验证率**：客户端通过PoL验证的比例
- **检测率**：对恶意客户端的检测成功率
- **通信成本**：数据传输量
- **存储成本**：PoL证明存储开销

## ⚙️ 配置说明

### 默认配置
- **数据集**：FashionMNIST（快速）
- **模型**：simpleCNN（轻量级）
- **客户端数量**：5-10个
- **训练轮数**：2-5轮
- **PoL验证**：默认启用

### 自定义配置
编辑 `config.yaml` 文件可以修改默认设置：
```yaml
datasets: [FashionMNIST, CIFAR10, CIFAR100]
models: [simpleCNN, resnet18, resnet34]
client_nums: [5, 10, 20]
communication_rounds: [2, 5, 10]
```

## 🔧 故障排除

### 常见问题

#### 1. 区块链连接失败
```bash
# 检查Ganache是否运行
ps aux | grep ganache

# 重启区块链
pkill -f ganache
python run.py demo  # 会自动启动新的区块链
```

#### 2. GPU内存不足
```bash
# 使用CPU模式（自动检测）
python run.py demo

# 或减少客户端数量
python run.py custom  # 然后选择较少的客户端数量
```

#### 3. 实验中断
```bash
# 使用Ctrl+C安全中断
# 系统会自动清理资源
```

#### 4. 结果不一致
```bash
# 重新运行实验
python run.py demo

# 检查系统状态
python run.py demo  # 第一次运行作为基准
```

## 📞 获取帮助

### 查看帮助信息
```bash
python run.py --help
python experiments.py --help
```

### 交互式帮助
```bash
python run.py  # 选择选项6进入自定义模式，有详细说明
```

### 实验模式说明
- **demo/test**：快速验证系统功能
- **compare**：对比PoL和传统方法
- **attack**：测试攻击防御能力
- **scale**：测试可扩展性
- **paper**：完整论文实验
- **custom**：自定义配置

## 🎉 开始实验

现在就试试最简单的方式：
```bash
cd VeryFL-PoL-main
python run.py
```

选择 `1` 开始快速测试，2分钟后你就能看到PoL的神奇效果！
