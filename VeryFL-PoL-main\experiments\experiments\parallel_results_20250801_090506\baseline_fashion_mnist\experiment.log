2025-08-01 09:05:08,681 - task - WARNING - 实验监控系统不可用
2025-08-01 09:05:08,683 - chainfl.connection_manager - INFO - 🔗 初始化区块链连接管理器
2025-08-01 09:05:10,223 - chainfl.interact - INFO - ✅ 成功加载Token合约
2025-08-01 09:05:10,223 - chainfl.interact - INFO - ✅ 成功加载合约: ['NetworkManager', 'PoLManager', 'SimplePoLManager', 'SimpleStorage', 'clientManager', 'watermarkNegotiation', 'interface']
2025-08-01 09:05:10,223 - chainfl.connection_manager - INFO - 🔄 切换到chainEnv目录: /mnt/persist/workspace/VeryFL-PoL-main/chainEnv
2025-08-01 09:05:10,327 - chainfl.connection_manager - INFO - ✅ 成功加载Brownie项目
2025-08-01 09:05:10,327 - chainfl.connection_manager - INFO - 📋 项目加载后可用网络: ['anvil', 'anvil-fork', 'development', 'geth-dev', 'hardhat', 'hardhat-fork', 'mainnet-fork', 'bsc-main-fork', 'ftm-main-fork', 'polygon-main-fork', 'avax-main-fork', 'aurora-main-fork', 'gnosis-main-fork', 'zkevm-main-fork', 'base-main-fork', 'mainnet', 'ropsten', 'rinkeby', 'sepolia', 'goerli', 'kovan', 'etc', 'kotti', 'arbitrum-main', 'avax-main', 'avax-test', 'aurora-main', 'aurora-test', 'bsc-test', 'bsc-main', 'boba-test', 'boba-main', 'ftm-test', 'ftm-main', 'harmony-main', 'hedera-main', 'hedera-test', 'hedera-preview', 'moonbeam-main', 'moonbeam-test', 'moonriver-main', 'optimism-main', 'optimism-test', 'polygon-main', 'polygon-test', 'gnosis-main', 'gnosis-test', 'zkevm-main', 'base-main']
2025-08-01 09:05:10,327 - chainfl.connection_manager - INFO - 🎯 选择网络: development
2025-08-01 09:05:10,327 - chainfl.connection_manager - INFO - 🔗 尝试连接到development网络...
/home/<USER>/.local/lib/python3.10/site-packages/brownie/network/main.py:44: BrownieEnvironmentWarning: Development network has a block height of 2
  warnings.warn(
2025-08-01 09:05:10,717 - chainfl.connection_manager - INFO - ✅ 成功连接到development网络
2025-08-01 09:05:10,718 - chainfl.connection_manager - INFO - 🌐 区块链网络信息:
2025-08-01 09:05:10,718 - chainfl.connection_manager - INFO -    📡 网络名称: development
2025-08-01 09:05:10,718 - chainfl.connection_manager - INFO -    🔗 RPC地址: http://127.0.0.1:8545
2025-08-01 09:05:10,719 - chainfl.connection_manager - INFO -    ⛓️  Chain ID: 1337
2025-08-01 09:05:10,720 - chainfl.connection_manager - INFO -    💰 可用账户: 10
2025-08-01 09:05:10,720 - chainfl.connection_manager - INFO -    🏦 默认账户: 0x66aB6D9362d4F35596279692F0251Db635165871
2025-08-01 09:05:10,720 - chainfl.connection_manager - INFO - 📝 部署水印协商合约...
2025-08-01 09:05:10,771 - chainfl.connection_manager - INFO - ✅ 水印协商合约部署成功，Gas消耗: 187993
2025-08-01 09:05:10,771 - chainfl.connection_manager - INFO - 👥 部署客户端管理合约...
Attached to local RPC client listening at '127.0.0.1:8545'...

Transaction sent: [0;1;34m0x7c6b39f60bef7a3e185a4f09b66c2dca4fcce637a73513abf14e2b21dcbcb28a[0;m
  Gas price: [0;1;34m25.0[0;m gwei   Gas limit: [0;1;34m8000000[0;m   Nonce: [0;1;34m2[0;m

  watermarkNegotiation.constructor confirmed   Block: [0;1;34m3[0;m   Gas used: [0;1;34m187993[0;m ([0;1;34m2.35%[0;m)
  watermarkNegotiation deployed at: [0;1;34m0xE7eD6747FaC5360f88a2EFC03E00d25789F69291[0;m


Transaction sent: [0;1;34m0x38eb15eb69dd9653e4b3cac2d49d0e159926c21dfcfe8f1e649de523fff3cf3e[0;m
  Awaiting transaction in the mempool... -
  Awaiting transaction in the mempool... \
  Awaiting transaction in the mempool... |
  Awaiting transaction in the mempool... /
  Awaiting transaction in the mempool... -
  Awaiting transaction in the mempool... \
  Awaiting transaction in the mempool... |
  Awaiting transaction in the mempool... /
  Awaiting transaction in the mempool... -
  Awaiting transaction in the mempool... \
  Awaiting transaction in the mempool... |
  Awaiting transaction in the mempool... /
  Awaiting transaction in the mempool... -
  Awaiting transaction in the mempool... \
  Awaiting transaction in the mempool... |
  Awaiting transaction in the mempool... /
  Awaiting transaction in the mempool... -
  Awaiting transaction in the mempool... \
  Awaiting transaction in the mempool... |
  Awaiting transaction in the mempool... /
  Awaiting transaction in the mempool... -
  Awaiting transaction in the mempool... \
  Awaiting transaction in the mempool... |
  Awaiting transaction in the mempool... /
  Awaiting transaction in the mempool... -
  Awaiting transaction in the mempool... \
  Awaiting transaction in the mempool... |
  Awaiting transaction in the mempool... /
  Awaiting transaction in the mempool... -
  Awaiting transaction in the mempool... \
  Awaiting transaction in the mempool... |
  Awaiting transaction in the mempool... /
  Awaiting transaction in the mempool... -
  Awaiting transaction in the mempool... \
  Awaiting transaction in the mempool... |
  Awaiting transaction in the mempool... /
  Awaiting transaction in the mempool... -
  Awaiting transaction in the mempool... \
  Awaiting transaction in the mempool... |
  Awaiting transaction in the mempool... /
  Awaiting transaction in the mempool... -
  Awaiting transaction in the mempool... \
  Awaiting transaction in the mempool... |
  Awaiting transaction in the mempool... /
  Awaiting transaction in the mempool... -
  Awaiting transaction in the mempool... \
  Awaiting transaction in the mempool... |
  Awaiting transaction in the mempool... /
  Awaiting transaction in the mempool... -
  Awaiting transaction in the mempool... \
  Awaiting transaction in the mempool... |
  Awaiting transaction in the mempool... /
  Awaiting transaction in the mempool... -
  Awaiting transaction in the mempool... \
  Awaiting transaction in the mempool... |
  Awaiting transaction in the mempool... /
  Awaiting transaction in the mempool... -
  Awaiting transaction in the mempool... \
  Awaiting transaction in the mempool... |
  Awaiting transaction in the mempool... /
  Awaiting transaction in the mempool... -
  Awaiting transaction in the mempool... \
