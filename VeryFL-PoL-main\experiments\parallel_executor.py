#!/usr/bin/env python3
"""
VeryFL-PoL 并行实验执行器
简洁、高效、支持GPU/CPU自动检测的并行实验执行器
"""

import os
import sys
import json
import time
import logging
import argparse
import subprocess
import threading
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

@dataclass
class ExperimentConfig:
    """实验配置"""
    name: str
    benchmark: str
    communication_rounds: int
    enable_pol: bool = True
    client_num: int = 10
    timeout: int = 1800  # 30分钟超时
    attack_type: Optional[str] = None
    malicious_ratio: float = 0.0
    compression_method: Optional[str] = None
    network_condition: Optional[str] = None
    
    def to_command(self) -> List[str]:
        """转换为命令行参数"""
        cmd = [
            "python", "test.py",  # 修复：使用test.py而不是task.py
            "--benchmark", self.benchmark,
            "--communication-rounds", str(self.communication_rounds)
        ]

        if self.enable_pol:
            cmd.append("--enable-pol")

        if self.client_num != 10:  # 默认值
            cmd.extend(["--client-num", str(self.client_num)])

        if self.attack_type:
            cmd.extend(["--attack-type", self.attack_type])
            cmd.extend(["--malicious-ratio", str(self.malicious_ratio)])

        if self.compression_method:
            cmd.extend(["--compression-method", self.compression_method])

        if self.network_condition:
            cmd.extend(["--network-condition", self.network_condition])

        return cmd

@dataclass
class ExperimentResult:
    """实验结果"""
    config: ExperimentConfig
    success: bool
    start_time: str
    end_time: str
    duration: float
    error_message: Optional[str] = None
    log_file: Optional[str] = None

class ParallelExecutor:
    """并行实验执行器"""
    
    def __init__(self, max_parallel: int = 2, output_dir: str = None):
        self.max_parallel = max_parallel
        self.output_dir = output_dir or f"experiments/parallel_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.results: List[ExperimentResult] = []
        self.setup_output_dir()
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler(f"{self.output_dir}/executor.log")
            ]
        )
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"📁 输出目录: {self.output_dir}")
    
    def setup_output_dir(self):
        """设置输出目录"""
        Path(self.output_dir).mkdir(parents=True, exist_ok=True)
    
    def detect_environment(self):
        """检测运行环境"""
        try:
            import torch
            if torch.cuda.is_available():
                gpu_count = torch.cuda.device_count()
                gpu_name = torch.cuda.get_device_name(0)
                self.logger.info(f"🚀 检测到 {gpu_count} 个GPU: {gpu_name}")
                return "gpu"
            else:
                self.logger.info("🔧 使用CPU环境")
                return "cpu"
        except ImportError:
            self.logger.warning("⚠️ PyTorch未安装，假设CPU环境")
            return "cpu"
    
    def run_single_experiment(self, config: ExperimentConfig) -> ExperimentResult:
        """运行单个实验"""
        start_time = datetime.now()
        start_time_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
        
        self.logger.info(f"🚀 开始实验: {config.name}")
        
        # 准备命令和环境
        cmd = config.to_command()
        env = os.environ.copy()
        
        # 设置实验特定的输出目录
        exp_dir = f"{self.output_dir}/{config.name}"
        Path(exp_dir).mkdir(parents=True, exist_ok=True)
        log_file = f"{exp_dir}/experiment.log"
        
        try:
            # 运行实验
            cwd = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            with open(log_file, 'w') as f:
                process = subprocess.run(
                    cmd,
                    cwd=cwd,
                    stdout=f,
                    stderr=subprocess.STDOUT,
                    timeout=config.timeout,
                    env=env,
                    text=True
                )
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            success = process.returncode == 0
            error_msg = None if success else f"进程退出码: {process.returncode}"
            
            self.logger.info(f"{'✅' if success else '❌'} 实验完成: {config.name} ({duration:.1f}s)")
            
            return ExperimentResult(
                config=config,
                success=success,
                start_time=start_time_str,
                end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
                duration=duration,
                error_message=error_msg,
                log_file=log_file
            )
            
        except subprocess.TimeoutExpired:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            self.logger.error(f"⏰ 实验超时: {config.name} ({config.timeout}s)")
            
            return ExperimentResult(
                config=config,
                success=False,
                start_time=start_time_str,
                end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
                duration=duration,
                error_message=f"实验超时 ({config.timeout}s)",
                log_file=log_file
            )
            
        except Exception as e:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            self.logger.error(f"💥 实验异常: {config.name} - {e}")
            
            return ExperimentResult(
                config=config,
                success=False,
                start_time=start_time_str,
                end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
                duration=duration,
                error_message=str(e),
                log_file=log_file
            )
    
    def run_experiments(self, configs: List[ExperimentConfig]) -> List[ExperimentResult]:
        """并行运行多个实验"""
        self.logger.info(f"🎯 开始并行执行 {len(configs)} 个实验 (并行度: {self.max_parallel})")

        # 检测环境
        env_type = self.detect_environment()

        # 显示GPU状态
        if env_type == "gpu":
            try:
                from .gpu_manager import get_gpu_status
                self.logger.info(get_gpu_status())
            except ImportError:
                pass

        # 并行执行
        with ThreadPoolExecutor(max_workers=self.max_parallel) as executor:
            future_to_config = {
                executor.submit(self.run_single_experiment, config): config
                for config in configs
            }

            for future in as_completed(future_to_config):
                result = future.result()
                self.results.append(result)

                # 定期显示GPU状态
                if env_type == "gpu" and len(self.results) % 2 == 0:
                    try:
                        from .gpu_manager import get_gpu_status
                        self.logger.info(f"📊 进度更新 ({len(self.results)}/{len(configs)})")
                        self.logger.info(get_gpu_status())
                    except ImportError:
                        pass

        # 生成报告
        self.generate_report()

        return self.results
    
    def generate_report(self):
        """生成实验报告"""
        report_file = f"{self.output_dir}/experiment_report.json"
        
        # 统计信息
        total_experiments = len(self.results)
        successful_experiments = sum(1 for r in self.results if r.success)
        failed_experiments = total_experiments - successful_experiments
        total_duration = sum(r.duration for r in self.results)
        
        # 生成报告
        report = {
            "summary": {
                "total_experiments": total_experiments,
                "successful_experiments": successful_experiments,
                "failed_experiments": failed_experiments,
                "success_rate": successful_experiments / total_experiments if total_experiments > 0 else 0,
                "total_duration": total_duration,
                "average_duration": total_duration / total_experiments if total_experiments > 0 else 0
            },
            "experiments": [asdict(result) for result in self.results]
        }
        
        # 保存报告
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # 打印摘要
        self.logger.info("="*60)
        self.logger.info("📊 实验执行报告")
        self.logger.info("="*60)
        self.logger.info(f"总实验数: {total_experiments}")
        self.logger.info(f"成功实验: {successful_experiments}")
        self.logger.info(f"失败实验: {failed_experiments}")
        self.logger.info(f"成功率: {successful_experiments/total_experiments*100:.1f}%")
        self.logger.info(f"总耗时: {total_duration:.1f}秒")
        self.logger.info(f"平均耗时: {total_duration/total_experiments:.1f}秒")
        self.logger.info(f"📄 详细报告: {report_file}")
        self.logger.info("="*60)

# 预定义实验套件 - 按照用户的5个实验设计重新组织
EXPERIMENT_SUITES = {
    # 快速测试套件
    "quick": [
        ExperimentConfig("pol_fashion_mnist_1", "PoLFashionMNIST", 1, True),
        ExperimentConfig("baseline_fashion_mnist_1", "FashionMNIST", 1, False),
    ],

    # 实验1：基础性能对比 - 原版VeryFL vs VeryFL-PoL
    "performance": [
        ExperimentConfig("baseline_fashion_mnist", "FashionMNIST", 5, False),
        ExperimentConfig("pol_fashion_mnist", "PoLFashionMNIST", 5, True),
        ExperimentConfig("baseline_cifar10", "CIFAR10", 5, False),
        ExperimentConfig("pol_cifar10", "PoLCIFAR10", 5, True),
        ExperimentConfig("baseline_cifar100", "CIFAR100", 5, False),
        ExperimentConfig("pol_cifar100", "PoLCIFAR100", 5, True),
    ],

    # 实验2：攻击防御效果 - 不同攻击类型和比例
    "attack_defense": [
        # 完全搭便车攻击
        ExperimentConfig("attack_free_rider_10", "PoLFashionMNIST", 5, True,
                        attack_type="free_rider", malicious_ratio=0.1),
        ExperimentConfig("attack_free_rider_20", "PoLFashionMNIST", 5, True,
                        attack_type="free_rider", malicious_ratio=0.2),
        ExperimentConfig("attack_free_rider_30", "PoLFashionMNIST", 5, True,
                        attack_type="free_rider", malicious_ratio=0.3),
        # 部分搭便车攻击
        ExperimentConfig("attack_partial_20", "PoLFashionMNIST", 5, True,
                        attack_type="partial_free_rider", malicious_ratio=0.2),
        # 数据投毒攻击
        ExperimentConfig("attack_data_poison_10", "PoLFashionMNIST", 5, True,
                        attack_type="data_poison", malicious_ratio=0.1),
        # 模型投毒攻击
        ExperimentConfig("attack_model_poison_10", "PoLFashionMNIST", 5, True,
                        attack_type="model_poison", malicious_ratio=0.1),
    ],

    # 实验3：压缩效果分析 - 不同压缩方案
    "compression": [
        ExperimentConfig("compression_none", "PoLFashionMNIST", 5, True,
                        compression_method="none"),
        ExperimentConfig("compression_quantization", "PoLFashionMNIST", 5, True,
                        compression_method="quantization"),
        ExperimentConfig("compression_incremental", "PoLFashionMNIST", 5, True,
                        compression_method="incremental"),
    ],

    # 完整实验套件（原有的full套件保持兼容）
    "full": [
        ExperimentConfig("pol_fashion_mnist_5", "PoLFashionMNIST", 5, True),
        ExperimentConfig("pol_cifar10_5", "PoLCIFAR10", 5, True),
        ExperimentConfig("baseline_fashion_mnist_5", "FashionMNIST", 5, False),
        ExperimentConfig("baseline_cifar10_5", "CIFAR10", 5, False),
    ]
}

def main():
    parser = argparse.ArgumentParser(description="VeryFL-PoL 并行实验执行器")
    parser.add_argument("--suite", choices=EXPERIMENT_SUITES.keys(), default="quick",
                       help="实验套件选择")
    parser.add_argument("--max-parallel", type=int, default=4,
                       help="最大并行实验数（双GPU推荐4-6）")
    parser.add_argument("--output-dir", type=str,
                       help="输出目录")
    parser.add_argument("--timeout", type=int, default=1800,
                       help="单个实验超时时间（秒）")

    args = parser.parse_args()
    
    # 获取实验配置
    configs = EXPERIMENT_SUITES[args.suite]

    # 更新配置的超时时间
    for config in configs:
        config.timeout = args.timeout

    # 创建执行器并运行
    executor = ParallelExecutor(
        max_parallel=args.max_parallel,
        output_dir=args.output_dir
    )
    
    results = executor.run_experiments(configs)
    
    # 返回成功状态
    success_count = sum(1 for r in results if r.success)
    return 0 if success_count == len(results) else 1

if __name__ == "__main__":
    exit(main())
