# VeryFL-PoL 统一架构迁移指南

## 🎯 新架构概述

我们已经成功将8种不同的启动方式统一为2个简洁的入口：

### 📁 新的统一入口

1. **`run.py`** - 单个实验入口
2. **`experiments.py`** - 批量实验管理器
3. **`config.yaml`** - 统一配置文件

### 🗑️ 已替代的旧文件

以下文件的功能已被新架构完全替代：

#### 基础启动脚本
- ~~`task.py`~~ → 使用 `run.py basic`
- ~~`test.py`~~ → 使用 `run.py basic`

#### 实验脚本
- ~~`experiments/parallel_executor.py`~~ → 使用 `experiments.py suite`
- ~~`experiments/master_experiment_runner.py`~~ → 使用 `experiments.py`
- ~~`experiments/network_simulator.py`~~ → 使用 `experiments.py network`
- ~~`experiments/compression_comparison.py`~~ → 使用 `experiments.py compression`
- ~~`experiments/sota_comparison.py`~~ → 集成到 `experiments.py`
- ~~`experiments/restart_experiments.py`~~ → 不再需要

## 🚀 迁移命令对照表

### 基础实验
```bash
# 旧方式
python task.py
python test.py --benchmark FashionMNIST --communication-rounds 5

# 新方式
python run.py basic --dataset FashionMNIST --rounds 5
python run.py quick-test  # 快速测试
```

### 攻击实验
```bash
# 旧方式
python test.py --benchmark PoLFashionMNIST --attack-type model_poison --malicious-ratio 0.1

# 新方式
python run.py attack --type model_poison --ratio 0.1
```

### 方法对比
```bash
# 旧方式
# 需要手动运行多个脚本

# 新方式
python run.py comparison --methods pol,fedavg,krum
```

### 批量实验
```bash
# 旧方式
python experiments/parallel_executor.py --suite quick
python experiments/master_experiment_runner.py --experiment all

# 新方式
python experiments.py suite --name quick
python experiments.py suite --name performance
```

### 网络实验
```bash
# 旧方式
python experiments/network_simulator.py --client-counts 10,20,50

# 新方式
python experiments.py network --clients 10,20,50
```

### 压缩实验
```bash
# 旧方式
python experiments/compression_comparison.py --test-scenarios small_model

# 新方式
python experiments.py compression --methods all
```

## 📊 配置文件迁移

### 旧方式：分散的配置
- `config/benchmark.py` - 基准配置
- 各种脚本内的硬编码参数
- 重复的参数定义

### 新方式：统一配置
- `config.yaml` - 所有配置集中管理
- 预定义实验套件
- 智能默认值

## 🔧 高级功能

### 新增功能
1. **智能GPU分配** - 自动检测和分配GPU资源
2. **统一日志系统** - 一致的日志格式和级别
3. **实验套件** - 预定义的实验组合
4. **配置验证** - 自动检查配置有效性
5. **错误恢复** - 智能的错误处理和重试

### 保持不变的核心
- PoL模块（`pol/`）- 完全保持不变
- VeryFL核心（`task.py`, `config/benchmark.py`）- 保持兼容
- 区块链集成（`chainfl/`）- 功能增强但接口不变

## 🧪 验证新架构

### 快速验证
```bash
# 1. 快速测试
python run.py quick-test

# 2. 基础实验
python run.py basic --dataset FashionMNIST --rounds 3

# 3. 批量实验
python experiments.py suite --name quick
```

### 完整验证
```bash
# 运行所有实验套件
python experiments.py suite --name performance
python experiments.py suite --name attack_defense
python experiments.py suite --name compression
```

## 📈 性能改进

### 启动时间
- 旧架构：需要分别启动多个脚本
- 新架构：统一入口，减少重复初始化

### 资源管理
- 旧架构：手动管理GPU和内存
- 新架构：智能资源分配和释放

### 错误处理
- 旧架构：各脚本独立的错误处理
- 新架构：统一的错误恢复机制

## 🔄 回滚方案

如果需要回滚到旧架构：

1. 旧文件仍然保留在项目中
2. 可以继续使用原有的启动方式
3. 新旧架构可以并存

## 📝 注意事项

### 兼容性
- ✅ 所有原有功能都已迁移
- ✅ 配置格式向后兼容
- ✅ 输出格式保持一致

### 依赖关系
- ✅ 无新增外部依赖
- ✅ Python版本要求不变
- ✅ 区块链环境要求不变

### 数据格式
- ✅ 实验结果格式不变
- ✅ PoL证明格式不变
- ✅ 区块链交互格式不变

## 🎉 迁移完成

新的统一架构已经成功部署并通过测试！

- 🚀 8种启动方式 → 2个统一入口
- 📊 分散配置 → 统一配置文件
- 🔧 手动资源管理 → 智能自动管理
- 📈 重复代码 → 模块化设计

享受更简洁、更强大的VeryFL-PoL实验体验！
