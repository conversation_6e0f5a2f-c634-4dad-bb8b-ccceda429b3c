2025-08-01 09:05:08,730 - task - WARNING - 实验监控系统不可用
2025-08-01 09:05:08,732 - chainfl.connection_manager - INFO - 🔗 初始化区块链连接管理器
2025-08-01 09:05:10,294 - chainfl.interact - INFO - ✅ 成功加载Token合约
2025-08-01 09:05:10,295 - chainfl.interact - INFO - ✅ 成功加载合约: ['NetworkManager', 'PoLManager', 'SimplePoLManager', 'SimpleStorage', 'clientManager', 'watermarkNegotiation', 'interface']
2025-08-01 09:05:10,295 - chainfl.connection_manager - INFO - 🔄 切换到chainEnv目录: /mnt/persist/workspace/VeryFL-PoL-main/chainEnv
2025-08-01 09:05:10,397 - chainfl.connection_manager - INFO - ✅ 成功加载Brownie项目
2025-08-01 09:05:10,397 - chainfl.connection_manager - INFO - 📋 项目加载后可用网络: ['anvil', 'anvil-fork', 'development', 'geth-dev', 'hardhat', 'hardhat-fork', 'mainnet-fork', 'bsc-main-fork', 'ftm-main-fork', 'polygon-main-fork', 'avax-main-fork', 'aurora-main-fork', 'gnosis-main-fork', 'zkevm-main-fork', 'base-main-fork', 'mainnet', 'ropsten', 'rinkeby', 'sepolia', 'goerli', 'kovan', 'etc', 'kotti', 'arbitrum-main', 'avax-main', 'avax-test', 'aurora-main', 'aurora-test', 'bsc-test', 'bsc-main', 'boba-test', 'boba-main', 'ftm-test', 'ftm-main', 'harmony-main', 'hedera-main', 'hedera-test', 'hedera-preview', 'moonbeam-main', 'moonbeam-test', 'moonriver-main', 'optimism-main', 'optimism-test', 'polygon-main', 'polygon-test', 'gnosis-main', 'gnosis-test', 'zkevm-main', 'base-main']
2025-08-01 09:05:10,397 - chainfl.connection_manager - INFO - 🎯 选择网络: development
2025-08-01 09:05:10,397 - chainfl.connection_manager - INFO - 🔗 尝试连接到development网络...
/home/<USER>/.local/lib/python3.10/site-packages/brownie/network/main.py:44: BrownieEnvironmentWarning: Development network has a block height of 2
  warnings.warn(
2025-08-01 09:05:10,760 - chainfl.connection_manager - INFO - ✅ 成功连接到development网络
2025-08-01 09:05:10,760 - chainfl.connection_manager - INFO - 🌐 区块链网络信息:
2025-08-01 09:05:10,760 - chainfl.connection_manager - INFO -    📡 网络名称: development
2025-08-01 09:05:10,760 - chainfl.connection_manager - INFO -    🔗 RPC地址: http://127.0.0.1:8545
2025-08-01 09:05:10,762 - chainfl.connection_manager - INFO -    ⛓️  Chain ID: 1337
2025-08-01 09:05:10,762 - chainfl.connection_manager - INFO -    💰 可用账户: 10
2025-08-01 09:05:10,762 - chainfl.connection_manager - INFO -    🏦 默认账户: 0x66aB6D9362d4F35596279692F0251Db635165871
2025-08-01 09:05:10,762 - chainfl.connection_manager - INFO - 📝 部署水印协商合约...
2025-08-01 09:05:10,819 - chainfl.connection_manager - INFO - ✅ 水印协商合约部署成功，Gas消耗: 187993
2025-08-01 09:05:10,819 - chainfl.connection_manager - INFO - 👥 部署客户端管理合约...
2025-08-01 09:05:10,861 - chainfl.connection_manager - INFO - ✅ 客户端管理合约部署成功，Gas消耗: 194015
2025-08-01 09:05:10,861 - chainfl.connection_manager - INFO - 🔄 恢复工作目录: /mnt/persist/workspace/VeryFL-PoL-main
2025-08-01 09:05:10,862 - chainfl.interact - INFO - ✅ 区块链连接成功
2025-08-01 09:05:11,925 - util.device_manager - INFO - ⚠️ 未检测到CUDA设备，使用CPU计算
2025-08-01 09:05:11,925 - util.device_manager - INFO - 💡 如需GPU加速，请安装CUDA版本的PyTorch
2025-08-01 09:05:11,927 - __main__ - INFO - 获取基准测试: PoLFashionMNIST
2025-08-01 09:05:11,927 - config.benchmark - INFO - Initializing Benchmark PoLFashionMNIST
2025-08-01 09:05:11,927 - util.device_manager - INFO - 🔄 自动检测设备: cpu
2025-08-01 09:05:11,927 - __main__ - INFO - 设置通信轮数: 5
2025-08-01 09:05:11,927 - __main__ - INFO - 启用PoL验证
2025-08-01 09:05:11,927 - __main__ - INFO - --训练开始--
2025-08-01 09:05:11,927 - __main__ - INFO - 获取全局参数 - 数据集: FashionMNIST, 模型: simpleCNN
2025-08-01 09:05:11,927 - task - INFO - 🔧 未检测到GPU，使用CPU设备
2025-08-01 09:05:11,927 - task - INFO - ✅ 设备设置: cpu
2025-08-01 09:05:11,927 - task - INFO - Constructing dataset FashionMNIST from dataset Factory
2025-08-01 09:05:12,053 - task - INFO - Constructing Model from model factory with model simpleCNN and class_num 10
2025-08-01 09:05:12,059 - task - INFO - Algorithm: <config.algorithm.PoLFedAvg object at 0x7fee998c3c10>
2025-08-01 09:05:12,059 - pol.pol_verifier - INFO - PoL验证器使用CPU计算: cpu
2025-08-01 09:05:12,059 - pol.pol_verifier - INFO - PoL验证器初始化完成，验证预算Q: 1
2025-08-01 09:05:12,059 - pol.pol_verifier - INFO - 多距离度量验证: 启用
2025-08-01 09:05:12,059 - pol.pol_verifier - INFO - 动态阈值校准: 启用
2025-08-01 09:05:12,059 - pol.pol_verifier - INFO - 支持的距离度量: ['l1', 'l2', 'linf', 'cosine']
2025-08-01 09:05:12,059 - server.aggregation_alg.polAggregator - INFO - PoL聚合器初始化完成，已启用PoL验证 (验证预算Q=1)
2025-08-01 09:05:12,059 - server.aggregation_alg.polAggregator - INFO - PoL FedAvg聚合器初始化完成
2025-08-01 09:05:12,059 - chainfl.interact - INFO - 客户端注册请求
2025-08-01 09:05:12,059 - chainfl.interact - INFO - 客户端注册请求
2025-08-01 09:05:12,059 - chainfl.interact - INFO - 客户端注册请求
2025-08-01 09:05:12,059 - chainfl.interact - INFO - 客户端注册请求
2025-08-01 09:05:12,059 - chainfl.interact - INFO - 客户端注册请求
2025-08-01 09:05:12,059 - chainfl.interact - INFO - 客户端注册请求
2025-08-01 09:05:12,059 - chainfl.interact - INFO - 客户端注册请求
2025-08-01 09:05:12,059 - chainfl.interact - INFO - 客户端注册请求
2025-08-01 09:05:12,059 - chainfl.interact - INFO - 客户端注册请求
2025-08-01 09:05:12,059 - chainfl.interact - INFO - 客户端注册请求
2025-08-01 09:05:12,059 - task - INFO - Constructing dataloader with batch size 32, client_num: 10, non-iid: False
2025-08-01 09:05:24,204 - task - INFO - No client need to add watermark
2025-08-01 09:05:24,204 - chainfl.pol_blockchain_client - INFO - 使用已加载的Brownie项目
2025-08-01 09:05:24,208 - chainfl.pol_blockchain_client - INFO - 连接到网络: development
2025-08-01 09:05:24,209 - chainfl.pol_blockchain_client - INFO - 可用账户数量: 10
2025-08-01 09:05:24,209 - chainfl.pol_blockchain_client - INFO - 开始部署SimplePoLManager合约...
2025-08-01 09:05:24,209 - chainfl.pol_blockchain_client - INFO - 尝试部署合约 (第1次)，Gas价格: 25.0 Gwei
2025-08-01 09:05:24,266 - chainfl.pol_blockchain_client - INFO - ✅ SimplePoLManager合约部署成功: 0x6b4BDe1086912A6Cb24ce3dB43b3466e6c72AFd3
2025-08-01 09:05:24,322 - chainfl.pol_blockchain_client - INFO - 向激励池添加奖励: 10000
2025-08-01 09:05:24,322 - task - INFO - 激励池初始化完成，初始奖励: 10000
2025-08-01 09:05:24,377 - chainfl.pol_blockchain_client - INFO - 开始新一轮
2025-08-01 09:05:24,378 - task - INFO - 区块链状态已重置，开始新实验
2025-08-01 09:05:24,379 - pol.pol_verifier - INFO - PoL验证器使用CPU计算: cpu
2025-08-01 09:05:24,379 - pol.pol_verifier - INFO - PoL验证器初始化完成，验证预算Q: 1
2025-08-01 09:05:24,379 - pol.pol_verifier - INFO - 多距离度量验证: 启用
2025-08-01 09:05:24,379 - pol.pol_verifier - INFO - 动态阈值校准: 启用
2025-08-01 09:05:24,379 - pol.pol_verifier - INFO - 支持的距离度量: ['l1', 'l2', 'linf', 'cosine']
2025-08-01 09:05:24,379 - client.polClient - INFO - PoL客户端 client_0 初始化完成，PoL功能: 启用
2025-08-01 09:05:24,380 - pol.pol_verifier - INFO - PoL验证器使用CPU计算: cpu
2025-08-01 09:05:24,380 - pol.pol_verifier - INFO - PoL验证器初始化完成，验证预算Q: 1
2025-08-01 09:05:24,380 - pol.pol_verifier - INFO - 多距离度量验证: 启用
2025-08-01 09:05:24,380 - pol.pol_verifier - INFO - 动态阈值校准: 启用
2025-08-01 09:05:24,380 - pol.pol_verifier - INFO - 支持的距离度量: ['l1', 'l2', 'linf', 'cosine']
2025-08-01 09:05:24,380 - client.polClient - INFO - PoL客户端 client_1 初始化完成，PoL功能: 启用
2025-08-01 09:05:24,381 - pol.pol_verifier - INFO - PoL验证器使用CPU计算: cpu
2025-08-01 09:05:24,381 - pol.pol_verifier - INFO - PoL验证器初始化完成，验证预算Q: 1
2025-08-01 09:05:24,381 - pol.pol_verifier - INFO - 多距离度量验证: 启用
2025-08-01 09:05:24,381 - pol.pol_verifier - INFO - 动态阈值校准: 启用
2025-08-01 09:05:24,381 - pol.pol_verifier - INFO - 支持的距离度量: ['l1', 'l2', 'linf', 'cosine']
2025-08-01 09:05:24,382 - client.polClient - INFO - PoL客户端 client_2 初始化完成，PoL功能: 启用
2025-08-01 09:05:24,382 - pol.pol_verifier - INFO - PoL验证器使用CPU计算: cpu
2025-08-01 09:05:24,382 - pol.pol_verifier - INFO - PoL验证器初始化完成，验证预算Q: 1
2025-08-01 09:05:24,382 - pol.pol_verifier - INFO - 多距离度量验证: 启用
2025-08-01 09:05:24,383 - pol.pol_verifier - INFO - 动态阈值校准: 启用
2025-08-01 09:05:24,383 - pol.pol_verifier - INFO - 支持的距离度量: ['l1', 'l2', 'linf', 'cosine']
2025-08-01 09:05:24,383 - client.polClient - INFO - PoL客户端 client_3 初始化完成，PoL功能: 启用
2025-08-01 09:05:24,383 - pol.pol_verifier - INFO - PoL验证器使用CPU计算: cpu
2025-08-01 09:05:24,384 - pol.pol_verifier - INFO - PoL验证器初始化完成，验证预算Q: 1
2025-08-01 09:05:24,384 - pol.pol_verifier - INFO - 多距离度量验证: 启用
2025-08-01 09:05:24,384 - pol.pol_verifier - INFO - 动态阈值校准: 启用
2025-08-01 09:05:24,384 - pol.pol_verifier - INFO - 支持的距离度量: ['l1', 'l2', 'linf', 'cosine']
2025-08-01 09:05:24,384 - client.polClient - INFO - PoL客户端 client_4 初始化完成，PoL功能: 启用
2025-08-01 09:05:24,387 - pol.pol_verifier - INFO - PoL验证器使用CPU计算: cpu
2025-08-01 09:05:24,387 - pol.pol_verifier - INFO - PoL验证器初始化完成，验证预算Q: 1
2025-08-01 09:05:24,387 - pol.pol_verifier - INFO - 多距离度量验证: 启用
2025-08-01 09:05:24,388 - pol.pol_verifier - INFO - 动态阈值校准: 启用
2025-08-01 09:05:24,388 - pol.pol_verifier - INFO - 支持的距离度量: ['l1', 'l2', 'linf', 'cosine']
2025-08-01 09:05:24,388 - client.polClient - INFO - PoL客户端 client_5 初始化完成，PoL功能: 启用
2025-08-01 09:05:24,388 - pol.pol_verifier - INFO - PoL验证器使用CPU计算: cpu
2025-08-01 09:05:24,388 - pol.pol_verifier - INFO - PoL验证器初始化完成，验证预算Q: 1
2025-08-01 09:05:24,389 - pol.pol_verifier - INFO - 多距离度量验证: 启用
2025-08-01 09:05:24,389 - pol.pol_verifier - INFO - 动态阈值校准: 启用
2025-08-01 09:05:24,389 - pol.pol_verifier - INFO - 支持的距离度量: ['l1', 'l2', 'linf', 'cosine']
2025-08-01 09:05:24,389 - client.polClient - INFO - PoL客户端 client_6 初始化完成，PoL功能: 启用
2025-08-01 09:05:24,389 - pol.pol_verifier - INFO - PoL验证器使用CPU计算: cpu
2025-08-01 09:05:24,390 - pol.pol_verifier - INFO - PoL验证器初始化完成，验证预算Q: 1
2025-08-01 09:05:24,390 - pol.pol_verifier - INFO - 多距离度量验证: 启用
2025-08-01 09:05:24,390 - pol.pol_verifier - INFO - 动态阈值校准: 启用
2025-08-01 09:05:24,390 - pol.pol_verifier - INFO - 支持的距离度量: ['l1', 'l2', 'linf', 'cosine']
2025-08-01 09:05:24,390 - client.polClient - INFO - PoL客户端 client_7 初始化完成，PoL功能: 启用
2025-08-01 09:05:24,391 - pol.pol_verifier - INFO - PoL验证器使用CPU计算: cpu
2025-08-01 09:05:24,391 - pol.pol_verifier - INFO - PoL验证器初始化完成，验证预算Q: 1
2025-08-01 09:05:24,391 - pol.pol_verifier - INFO - 多距离度量验证: 启用
2025-08-01 09:05:24,391 - pol.pol_verifier - INFO - 动态阈值校准: 启用
2025-08-01 09:05:24,391 - pol.pol_verifier - INFO - 支持的距离度量: ['l1', 'l2', 'linf', 'cosine']
2025-08-01 09:05:24,391 - client.polClient - INFO - PoL客户端 client_8 初始化完成，PoL功能: 启用
2025-08-01 09:05:24,392 - pol.pol_verifier - INFO - PoL验证器使用CPU计算: cpu
2025-08-01 09:05:24,392 - pol.pol_verifier - INFO - PoL验证器初始化完成，验证预算Q: 1
2025-08-01 09:05:24,392 - pol.pol_verifier - INFO - 多距离度量验证: 启用
2025-08-01 09:05:24,392 - pol.pol_verifier - INFO - 动态阈值校准: 启用
2025-08-01 09:05:24,392 - pol.pol_verifier - INFO - 支持的距离度量: ['l1', 'l2', 'linf', 'cosine']
2025-08-01 09:05:24,392 - client.polClient - INFO - PoL客户端 client_9 初始化完成，PoL功能: 启用
2025-08-01 09:05:24,392 - task - INFO - 开始第 1/5 轮通信
2025-08-01 09:05:24,392 - task - INFO - 🚀 开始并行训练 10 个客户端（第1轮）
2025-08-01 09:05:24,392 - task - INFO - 设置并行训练超时时间: 471秒 (PoL: True, 设备: cpu)
2025-08-01 09:05:24,392 - client.polClient - INFO - 🚀 客户端 client_0 开始训练，PoL功能: 启用
2025-08-01 09:05:24,393 - client.polClient - INFO - 🚀 客户端 client_1 开始训练，PoL功能: 启用
2025-08-01 09:05:24,394 - client.polClient - INFO - 🔧 客户端 client_1 使用PoL训练器
2025-08-01 09:05:24,394 - client.polClient - INFO - 🚀 客户端 client_2 开始训练，PoL功能: 启用
2025-08-01 09:05:24,393 - client.polClient - INFO - 🔧 客户端 client_0 使用PoL训练器
2025-08-01 09:05:24,395 - pol.pol_generator - INFO - PoL生成器初始化完成，保存频率: None, 保存目录: ./experiments/pol_data, 压缩模式: 启用, 异步保存: 启用, 磁盘保存: 禁用(仅内存)
2025-08-01 09:05:24,395 - client.polClient - INFO - 🔧 客户端 client_2 使用PoL训练器
2025-08-01 09:05:24,396 - pol.pol_generator - INFO - PoL生成器初始化完成，保存频率: None, 保存目录: ./experiments/pol_data, 压缩模式: 启用, 异步保存: 启用, 磁盘保存: 禁用(仅内存)
2025-08-01 09:05:24,397 - client.trainer.polTrainer - INFO - PoL训练器初始化完成，已启用学习证明生成
2025-08-01 09:05:24,397 - pol.pol_generator - INFO - PoL生成器初始化完成，保存频率: None, 保存目录: ./experiments/pol_data, 压缩模式: 启用, 异步保存: 启用, 磁盘保存: 禁用(仅内存)
2025-08-01 09:05:24,397 - client.trainer.polTrainer - INFO - PoL训练器初始化完成，已启用学习证明生成
2025-08-01 09:05:24,397 - client.base.baseTrainer - INFO - Constructing Optimizer SGD: lr 0.01, weight_decay: 1e-05
2025-08-01 09:05:24,396 - client.trainer.polTrainer - INFO - PoL训练器初始化完成，已启用学习证明生成
2025-08-01 09:05:24,397 - client.base.baseTrainer - INFO - Constructing Optimizer SGD: lr 0.01, weight_decay: 1e-05
2025-08-01 09:05:24,398 - client.trainer.polTrainer - INFO - 计算得到每epoch步数: 188
2025-08-01 09:05:24,398 - client.base.baseTrainer - INFO - Constructing Optimizer SGD: lr 0.01, weight_decay: 1e-05
2025-08-01 09:05:24,398 - client.trainer.polTrainer - INFO - 计算得到每epoch步数: 188
2025-08-01 09:05:24,399 - pol.pol_generator - INFO - 按PoL论文设置k=S: 每epoch保存一次，k=188步
2025-08-01 09:05:24,399 - client.trainer.polTrainer - INFO - 计算得到每epoch步数: 188
2025-08-01 09:05:24,399 - pol.pol_generator - INFO - 按PoL论文设置k=S: 每epoch保存一次，k=188步
2025-08-01 09:05:24,400 - pol.pol_generator - INFO - 按PoL论文设置k=S: 每epoch保存一次，k=188步
2025-08-01 09:05:24,404 - pol.pol_generator - INFO - 客户端 client_2 开始PoL训练 (压缩模式: True, 保存频率: 188步)
2025-08-01 09:05:24,406 - pol.pol_generator - INFO - 客户端 client_1 开始PoL训练 (压缩模式: True, 保存频率: 188步)
2025-08-01 09:05:24,408 - pol.pol_generator - INFO - 客户端 client_0 开始PoL训练 (压缩模式: True, 保存频率: 188步)
Attached to local RPC client listening at '127.0.0.1:8545'...

Transaction sent: [0;1;34m0x39a75499611434d847c6d858e7f17d6f843c41de210181f85b53404e5561db7f[0;m
  Gas price: [0;1;34m25.0[0;m gwei   Gas limit: [0;1;34m8000000[0;m   Nonce: [0;1;34m3[0;m

  watermarkNegotiation.constructor confirmed   Block: [0;1;34m4[0;m   Gas used: [0;1;34m187993[0;m ([0;1;34m2.35%[0;m)
  watermarkNegotiation deployed at: [0;1;34m0x6951b5Bd815043E3F842c1b026b0Fa888Cc2DD85[0;m


Transaction sent: [0;1;34m0x6fce7e2e56fbb42e90b7a9f32b13bdb9fccc10772cea5b0f28c00a4e90a2a9c6[0;m
  Gas price: [0;1;34m25.0[0;m gwei   Gas limit: [0;1;34m8000000[0;m   Nonce: [0;1;34m4[0;m

  clientManager.constructor confirmed   Block: [0;1;34m6[0;m   Gas used: [0;1;34m194015[0;m ([0;1;34m2.43%[0;m)
  clientManager deployed at: [0;1;34m0xe0aA552A10d7EC8760Fc6c246D391E698a82dDf9[0;m


Transaction sent: [0;1;34m0xfe964493995832b29029b1c55452371f96fe728128a99da35281b49f8a5d4d0d[0;m
  Gas price: [0;1;34m25.0[0;m gwei   Gas limit: [0;1;34m8000000[0;m   Nonce: [0;1;34m5[0;m

  SimplePoLManager.constructor confirmed   Block: [0;1;34m7[0;m   Gas used: [0;1;34m893938[0;m ([0;1;34m11.17%[0;m)
  SimplePoLManager deployed at: [0;1;34m0x6b4BDe1086912A6Cb24ce3dB43b3466e6c72AFd3[0;m


Transaction sent: [0;1;34m0x8c5413398556bf3084c80f5d9ead3688f19bab466cf0c62a8ee856c0bd9496db[0;m
  Gas price: [0;1;34m25.0[0;m gwei   Gas limit: [0;1;34m300000[0;m   Nonce: [0;1;34m6[0;m

  SimplePoLManager.addToIncentivePool confirmed   Block: [0;1;34m8[0;m   Gas used: [0;1;34m42969[0;m ([0;1;34m14.32%[0;m)


  SimplePoLManager.addToIncentivePool confirmed   Block: [0;1;34m8[0;m   Gas used: [0;1;34m42969[0;m ([0;1;34m14.32%[0;m)


Transaction sent: [0;1;34m0x410a277f9f81aa34952969d6669074766e9ea15e4fe8cb5bfe49019254342221[0;m
  Gas price: [0;1;34m25.0[0;m gwei   Gas limit: [0;1;34m200000[0;m   Nonce: [0;1;34m7[0;m

  SimplePoLManager.startNewRound confirmed   Block: [0;1;34m9[0;m   Gas used: [0;1;34m27979[0;m ([0;1;34m13.99%[0;m)


  SimplePoLManager.startNewRound confirmed   Block: [0;1;34m9[0;m   Gas used: [0;1;34m27979[0;m ([0;1;34m13.99%[0;m)

2025-08-01 09:05:29,410 - pol.pol_generator - INFO - PoL证明仅保存在内存中（磁盘保存已禁用）
2025-08-01 09:05:29,410 - pol.pol_generator - INFO - 客户端 client_1 PoL证明生成完成，总步数: 188
2025-08-01 09:05:29,410 - client.polClient - INFO - ✅ 客户端 client_1 PoL训练完成，证明生成: 成功
2025-08-01 09:05:29,411 - client.clients - INFO - Epoch: 0, client id client_1
2025-08-01 09:05:29,411 - client.clients - INFO - Inner Epoch: 0 loss: 2.2704119796448565 epoch: 0 batch_count: 188 pol_summary: {'client_id': 'client_1', 'total_steps': 188, 'checkpoint_count': 2, 'batch_count': 188, 'save_freq': 188, 'is_training': True} 
2025-08-01 09:05:29,411 - client.polClient - INFO - 📊 客户端 client_1 训练结果显示完成
2025-08-01 09:05:29,411 - client.polClient - INFO - 客户端 client_1 生成PoL证明，总步数: 188
2025-08-01 09:05:29,411 - pol.pol_generator - INFO - PoL证明仅保存在内存中（磁盘保存已禁用）
2025-08-01 09:05:29,412 - pol.pol_generator - INFO - PoL证明仅保存在内存中（磁盘保存已禁用）
2025-08-01 09:05:29,414 - pol.pol_generator - INFO - 客户端 client_0 PoL证明生成完成，总步数: 188
2025-08-01 09:05:29,415 - client.polClient - INFO - ✅ 客户端 client_0 PoL训练完成，证明生成: 成功
2025-08-01 09:05:29,415 - client.clients - INFO - Epoch: 0, client id client_0
2025-08-01 09:05:29,412 - pol.pol_generator - INFO - 客户端 client_2 PoL证明生成完成，总步数: 188
2025-08-01 09:05:29,416 - client.clients - INFO - Inner Epoch: 0 loss: 2.271431011088351 epoch: 0 batch_count: 188 pol_summary: {'client_id': 'client_0', 'total_steps': 188, 'checkpoint_count': 2, 'batch_count': 188, 'save_freq': 188, 'is_training': True} 
2025-08-01 09:05:29,416 - client.polClient - INFO - ✅ 客户端 client_2 PoL训练完成，证明生成: 成功
2025-08-01 09:05:29,417 - client.polClient - INFO - 📊 客户端 client_0 训练结果显示完成
2025-08-01 09:05:29,418 - client.polClient - INFO - 客户端 client_0 生成PoL证明，总步数: 188
2025-08-01 09:05:29,418 - client.clients - INFO - Epoch: 0, client id client_2
2025-08-01 09:05:29,420 - client.clients - INFO - Inner Epoch: 0 loss: 2.2704583025993186 epoch: 0 batch_count: 188 pol_summary: {'client_id': 'client_2', 'total_steps': 188, 'checkpoint_count': 2, 'batch_count': 188, 'save_freq': 188, 'is_training': True} 
2025-08-01 09:05:29,421 - client.polClient - INFO - 📊 客户端 client_2 训练结果显示完成
2025-08-01 09:05:29,422 - client.polClient - INFO - 客户端 client_2 生成PoL证明，总步数: 188
2025-08-01 09:06:00,663 - client.clients - INFO - client id client_0 with inner epoch 0, Loss: 2.18775897064209, Acc: 13.919999999999998
2025-08-01 09:06:00,663 - client.clients - INFO - client id client_0 with inner epoch 0, Sign Accuarcy: -1.0
2025-08-01 09:06:00,664 - client.polClient - INFO - 客户端 client_0 PoL证明自验证通过
2025-08-01 09:06:00,665 - client.polClient - INFO - 🚀 客户端 client_3 开始训练，PoL功能: 启用
2025-08-01 09:06:00,666 - client.polClient - INFO - 🔧 客户端 client_3 使用PoL训练器
2025-08-01 09:06:00,667 - pol.pol_generator - INFO - PoL生成器初始化完成，保存频率: None, 保存目录: ./experiments/pol_data, 压缩模式: 启用, 异步保存: 启用, 磁盘保存: 禁用(仅内存)
2025-08-01 09:06:00,668 - client.trainer.polTrainer - INFO - PoL训练器初始化完成，已启用学习证明生成
2025-08-01 09:06:00,668 - client.base.baseTrainer - INFO - Constructing Optimizer SGD: lr 0.01, weight_decay: 1e-05
2025-08-01 09:06:00,668 - client.trainer.polTrainer - INFO - 计算得到每epoch步数: 188
2025-08-01 09:06:00,668 - pol.pol_generator - INFO - 按PoL论文设置k=S: 每epoch保存一次，k=188步
2025-08-01 09:06:00,673 - pol.pol_generator - INFO - 客户端 client_3 开始PoL训练 (压缩模式: True, 保存频率: 188步)
   📊 客户端 client_0 | 轮次 0 | 损失: 2.1878 | 准确率: 13.92% | 🔴 初期训练
2025-08-01 09:06:00,926 - client.clients - INFO - client id client_1 with inner epoch 0, Loss: 2.1804174865722654, Acc: 16.21
2025-08-01 09:06:00,928 - client.clients - INFO - client id client_1 with inner epoch 0, Sign Accuarcy: -1.0
2025-08-01 09:06:00,928 - client.polClient - INFO - 客户端 client_1 PoL证明自验证通过
2025-08-01 09:06:00,929 - client.polClient - INFO - 🚀 客户端 client_4 开始训练，PoL功能: 启用
2025-08-01 09:06:00,930 - client.polClient - INFO - 🔧 客户端 client_4 使用PoL训练器
2025-08-01 09:06:00,930 - pol.pol_generator - INFO - PoL生成器初始化完成，保存频率: None, 保存目录: ./experiments/pol_data, 压缩模式: 启用, 异步保存: 启用, 磁盘保存: 禁用(仅内存)
2025-08-01 09:06:00,931 - client.trainer.polTrainer - INFO - PoL训练器初始化完成，已启用学习证明生成
2025-08-01 09:06:00,932 - client.base.baseTrainer - INFO - Constructing Optimizer SGD: lr 0.01, weight_decay: 1e-05
2025-08-01 09:06:00,932 - client.trainer.polTrainer - INFO - 计算得到每epoch步数: 188
2025-08-01 09:06:00,932 - pol.pol_generator - INFO - 按PoL论文设置k=S: 每epoch保存一次，k=188步
2025-08-01 09:06:00,943 - pol.pol_generator - INFO - 客户端 client_4 开始PoL训练 (压缩模式: True, 保存频率: 188步)
   📊 客户端 client_1 | 轮次 0 | 损失: 2.1804 | 准确率: 16.21% | 🔴 初期训练
2025-08-01 09:06:01,370 - client.clients - INFO - client id client_2 with inner epoch 0, Loss: 2.180468852233887, Acc: 21.37
2025-08-01 09:06:01,372 - client.clients - INFO - client id client_2 with inner epoch 0, Sign Accuarcy: -1.0
2025-08-01 09:06:01,375 - client.polClient - INFO - 客户端 client_2 PoL证明自验证通过
2025-08-01 09:06:01,377 - client.polClient - INFO - 🚀 客户端 client_5 开始训练，PoL功能: 启用
2025-08-01 09:06:01,377 - client.polClient - INFO - 🔧 客户端 client_5 使用PoL训练器
2025-08-01 09:06:01,378 - pol.pol_generator - INFO - PoL生成器初始化完成，保存频率: None, 保存目录: ./experiments/pol_data, 压缩模式: 启用, 异步保存: 启用, 磁盘保存: 禁用(仅内存)
2025-08-01 09:06:01,379 - client.trainer.polTrainer - INFO - PoL训练器初始化完成，已启用学习证明生成
2025-08-01 09:06:01,379 - client.base.baseTrainer - INFO - Constructing Optimizer SGD: lr 0.01, weight_decay: 1e-05
2025-08-01 09:06:01,380 - client.trainer.polTrainer - INFO - 计算得到每epoch步数: 188
2025-08-01 09:06:01,380 - pol.pol_generator - INFO - 按PoL论文设置k=S: 每epoch保存一次，k=188步
2025-08-01 09:06:01,382 - pol.pol_generator - INFO - 客户端 client_5 开始PoL训练 (压缩模式: True, 保存频率: 188步)
   📊 客户端 client_2 | 轮次 0 | 损失: 2.1805 | 准确率: 21.37% | 🟡 学习中
2025-08-01 09:06:05,675 - pol.pol_generator - INFO - PoL证明仅保存在内存中（磁盘保存已禁用）
2025-08-01 09:06:05,676 - pol.pol_generator - INFO - 客户端 client_3 PoL证明生成完成，总步数: 188
2025-08-01 09:06:05,676 - client.polClient - INFO - ✅ 客户端 client_3 PoL训练完成，证明生成: 成功
2025-08-01 09:06:05,676 - client.clients - INFO - Epoch: 0, client id client_3
2025-08-01 09:06:05,676 - client.clients - INFO - Inner Epoch: 0 loss: 2.272771920295472 epoch: 0 batch_count: 188 pol_summary: {'client_id': 'client_3', 'total_steps': 188, 'checkpoint_count': 2, 'batch_count': 188, 'save_freq': 188, 'is_training': True} 
2025-08-01 09:06:05,676 - client.polClient - INFO - 📊 客户端 client_3 训练结果显示完成
2025-08-01 09:06:05,676 - client.polClient - INFO - 客户端 client_3 生成PoL证明，总步数: 188
2025-08-01 09:06:05,945 - pol.pol_generator - INFO - PoL证明仅保存在内存中（磁盘保存已禁用）
2025-08-01 09:06:05,945 - pol.pol_generator - INFO - 客户端 client_4 PoL证明生成完成，总步数: 188
2025-08-01 09:06:05,945 - client.polClient - INFO - ✅ 客户端 client_4 PoL训练完成，证明生成: 成功
2025-08-01 09:06:05,945 - client.clients - INFO - Epoch: 0, client id client_4
2025-08-01 09:06:05,945 - client.clients - INFO - Inner Epoch: 0 loss: 2.268968641757965 epoch: 0 batch_count: 188 pol_summary: {'client_id': 'client_4', 'total_steps': 188, 'checkpoint_count': 2, 'batch_count': 188, 'save_freq': 188, 'is_training': True} 
2025-08-01 09:06:05,945 - client.polClient - INFO - 📊 客户端 client_4 训练结果显示完成
2025-08-01 09:06:05,945 - client.polClient - INFO - 客户端 client_4 生成PoL证明，总步数: 188
2025-08-01 09:06:06,385 - pol.pol_generator - INFO - PoL证明仅保存在内存中（磁盘保存已禁用）
2025-08-01 09:06:06,386 - pol.pol_generator - INFO - 客户端 client_5 PoL证明生成完成，总步数: 188
2025-08-01 09:06:06,386 - client.polClient - INFO - ✅ 客户端 client_5 PoL训练完成，证明生成: 成功
2025-08-01 09:06:06,386 - client.clients - INFO - Epoch: 0, client id client_5
2025-08-01 09:06:06,386 - client.clients - INFO - Inner Epoch: 0 loss: 2.272217555248991 epoch: 0 batch_count: 188 pol_summary: {'client_id': 'client_5', 'total_steps': 188, 'checkpoint_count': 2, 'batch_count': 188, 'save_freq': 188, 'is_training': True} 
2025-08-01 09:06:06,386 - client.polClient - INFO - 📊 客户端 client_5 训练结果显示完成
2025-08-01 09:06:06,386 - client.polClient - INFO - 客户端 client_5 生成PoL证明，总步数: 188
