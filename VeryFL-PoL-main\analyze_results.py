#!/usr/bin/env python3
"""
VeryFL-PoL 实验结果分析工具
支持增量式实验结果的统计分析、图表生成和重复性验证
"""

import os
import sys
import json
import argparse
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class ExperimentAnalyzer:
    """实验结果分析器"""
    
    def __init__(self, results_dir: str = "VeryFL-PoL-main/experiments/results"):
        self.results_dir = Path(results_dir)
        self.analysis_dir = Path("VeryFL-PoL-main/experiments/analysis")
        self.analysis_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def list_experiments(self) -> Dict[str, List[str]]:
        """列出所有可用的实验和时间戳"""
        experiments = {}
        
        if not self.results_dir.exists():
            self.logger.warning(f"结果目录不存在: {self.results_dir}")
            return experiments
        
        for exp_dir in self.results_dir.iterdir():
            if exp_dir.is_dir():
                timestamps = []
                for ts_dir in exp_dir.iterdir():
                    if ts_dir.is_dir() and ts_dir.name.replace('_', '').replace('-', '').isdigit():
                        timestamps.append(ts_dir.name)
                
                if timestamps:
                    experiments[exp_dir.name] = sorted(timestamps)
        
        return experiments
    
    def load_experiment_data(self, experiment_name: str, timestamps: Optional[List[str]] = None) -> List[Dict]:
        """加载实验数据"""
        exp_dir = self.results_dir / experiment_name
        if not exp_dir.exists():
            self.logger.error(f"实验目录不存在: {exp_dir}")
            return []
        
        data = []
        
        # 如果没有指定时间戳，加载所有
        if timestamps is None:
            timestamps = [d.name for d in exp_dir.iterdir() if d.is_dir()]
        
        for timestamp in timestamps:
            ts_dir = exp_dir / timestamp
            if not ts_dir.exists():
                self.logger.warning(f"时间戳目录不存在: {ts_dir}")
                continue
            
            # 加载元数据
            metadata_file = ts_dir / "experiment_metadata.json"
            if metadata_file.exists():
                try:
                    with open(metadata_file, 'r') as f:
                        metadata = json.load(f)
                    
                    # 尝试解析实验结果
                    results = self._parse_experiment_results(ts_dir)
                    
                    data.append({
                        "timestamp": timestamp,
                        "metadata": metadata,
                        "results": results,
                        "path": str(ts_dir)
                    })
                    
                except Exception as e:
                    self.logger.error(f"加载实验数据失败 {ts_dir}: {e}")
        
        return data
    
    def _parse_experiment_results(self, result_dir: Path) -> Dict:
        """解析实验结果"""
        results = {}
        
        # 查找结果文件
        for file_path in result_dir.rglob("*.json"):
            if "metadata" in file_path.name:
                continue
            
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)
                    results[file_path.stem] = data
            except Exception as e:
                self.logger.debug(f"无法解析结果文件 {file_path}: {e}")
        
        # 查找日志文件中的关键指标
        for log_file in result_dir.rglob("*.log"):
            try:
                metrics = self._extract_metrics_from_log(log_file)
                if metrics:
                    results[f"{log_file.stem}_metrics"] = metrics
            except Exception as e:
                self.logger.debug(f"无法解析日志文件 {log_file}: {e}")
        
        return results
    
    def _extract_metrics_from_log(self, log_file: Path) -> Dict:
        """从日志文件中提取关键指标"""
        metrics = {}
        
        try:
            with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # 提取准确率
            import re
            accuracy_pattern = r'准确率[：:]\s*([0-9.]+)'
            accuracies = re.findall(accuracy_pattern, content)
            if accuracies:
                metrics['final_accuracy'] = float(accuracies[-1])
            
            # 提取训练时间
            time_pattern = r'训练时间[：:]\s*([0-9.]+)'
            times = re.findall(time_pattern, content)
            if times:
                metrics['training_time'] = float(times[-1])
            
            # 提取PoL验证相关指标
            pol_pattern = r'PoL验证[：:]\s*([0-9.]+)'
            pol_metrics = re.findall(pol_pattern, content)
            if pol_metrics:
                metrics['pol_verification'] = float(pol_metrics[-1])
        
        except Exception as e:
            self.logger.debug(f"提取指标失败 {log_file}: {e}")
        
        return metrics
    
    def analyze_experiment(self, experiment_name: str, timestamps: Optional[List[str]] = None) -> Dict:
        """分析单个实验的多次运行结果"""
        self.logger.info(f"分析实验: {experiment_name}")
        
        data = self.load_experiment_data(experiment_name, timestamps)
        if not data:
            self.logger.error(f"没有找到实验数据: {experiment_name}")
            return {}
        
        analysis = {
            "experiment_name": experiment_name,
            "total_runs": len(data),
            "timestamps": [d["timestamp"] for d in data],
            "statistics": {},
            "trends": {},
            "consistency": {}
        }
        
        # 提取所有数值指标
        metrics = self._extract_all_metrics(data)
        
        # 统计分析
        for metric_name, values in metrics.items():
            if values:
                analysis["statistics"][metric_name] = {
                    "mean": np.mean(values),
                    "std": np.std(values),
                    "min": np.min(values),
                    "max": np.max(values),
                    "count": len(values),
                    "cv": np.std(values) / np.mean(values) if np.mean(values) != 0 else 0
                }
        
        # 趋势分析
        analysis["trends"] = self._analyze_trends(metrics, analysis["timestamps"])
        
        # 一致性分析
        analysis["consistency"] = self._analyze_consistency(metrics)
        
        return analysis
    
    def _extract_all_metrics(self, data: List[Dict]) -> Dict[str, List[float]]:
        """提取所有数值指标"""
        metrics = {}
        
        for run_data in data:
            results = run_data.get("results", {})
            
            for key, value in results.items():
                if isinstance(value, dict):
                    for sub_key, sub_value in value.items():
                        if isinstance(sub_value, (int, float)):
                            metric_key = f"{key}_{sub_key}"
                            if metric_key not in metrics:
                                metrics[metric_key] = []
                            metrics[metric_key].append(float(sub_value))
                elif isinstance(value, (int, float)):
                    if key not in metrics:
                        metrics[key] = []
                    metrics[key].append(float(value))
        
        return metrics
    
    def _analyze_trends(self, metrics: Dict[str, List[float]], timestamps: List[str]) -> Dict:
        """分析趋势"""
        trends = {}
        
        for metric_name, values in metrics.items():
            if len(values) >= 2:
                # 简单线性趋势
                x = np.arange(len(values))
                slope = np.polyfit(x, values, 1)[0]
                
                trends[metric_name] = {
                    "slope": slope,
                    "direction": "increasing" if slope > 0 else "decreasing" if slope < 0 else "stable",
                    "magnitude": abs(slope)
                }
        
        return trends
    
    def _analyze_consistency(self, metrics: Dict[str, List[float]]) -> Dict:
        """分析一致性"""
        consistency = {}
        
        for metric_name, values in metrics.items():
            if len(values) >= 2:
                cv = np.std(values) / np.mean(values) if np.mean(values) != 0 else float('inf')
                
                if cv < 0.05:
                    level = "excellent"
                elif cv < 0.1:
                    level = "good"
                elif cv < 0.2:
                    level = "moderate"
                else:
                    level = "poor"
                
                consistency[metric_name] = {
                    "coefficient_of_variation": cv,
                    "consistency_level": level,
                    "range": np.max(values) - np.min(values)
                }
        
        return consistency
    
    def generate_report(self, experiment_name: str, analysis: Dict) -> str:
        """生成分析报告"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = self.analysis_dir / f"{experiment_name}_analysis_{timestamp}.md"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(f"# {experiment_name} 实验分析报告\n\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # 基本信息
            f.write("## 基本信息\n\n")
            f.write(f"- 实验名称: {analysis['experiment_name']}\n")
            f.write(f"- 总运行次数: {analysis['total_runs']}\n")
            f.write(f"- 时间戳: {', '.join(analysis['timestamps'])}\n\n")
            
            # 统计分析
            f.write("## 统计分析\n\n")
            for metric, stats in analysis['statistics'].items():
                f.write(f"### {metric}\n")
                f.write(f"- 平均值: {stats['mean']:.4f}\n")
                f.write(f"- 标准差: {stats['std']:.4f}\n")
                f.write(f"- 变异系数: {stats['cv']:.4f}\n")
                f.write(f"- 范围: [{stats['min']:.4f}, {stats['max']:.4f}]\n\n")
            
            # 一致性分析
            f.write("## 一致性分析\n\n")
            for metric, consistency in analysis['consistency'].items():
                f.write(f"### {metric}\n")
                f.write(f"- 一致性水平: {consistency['consistency_level']}\n")
                f.write(f"- 变异系数: {consistency['coefficient_of_variation']:.4f}\n\n")
        
        return str(report_file)
    
    def generate_plots(self, experiment_name: str, analysis: Dict) -> List[str]:
        """生成图表"""
        plot_files = []
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 为每个指标生成图表
        data = self.load_experiment_data(experiment_name)
        metrics = self._extract_all_metrics(data)
        
        for metric_name, values in metrics.items():
            if len(values) >= 2:
                plt.figure(figsize=(10, 6))
                
                # 时间序列图
                plt.subplot(1, 2, 1)
                plt.plot(range(len(values)), values, 'o-')
                plt.title(f'{metric_name} - 时间序列')
                plt.xlabel('实验次数')
                plt.ylabel(metric_name)
                plt.grid(True)
                
                # 箱线图
                plt.subplot(1, 2, 2)
                plt.boxplot(values)
                plt.title(f'{metric_name} - 分布')
                plt.ylabel(metric_name)
                
                plt.tight_layout()
                
                plot_file = self.analysis_dir / f"{experiment_name}_{metric_name}_{timestamp}.png"
                plt.savefig(plot_file, dpi=300, bbox_inches='tight')
                plt.close()
                
                plot_files.append(str(plot_file))
        
        return plot_files

def main():
    parser = argparse.ArgumentParser(description='VeryFL-PoL 实验结果分析工具')
    parser.add_argument('--experiment', '-e', type=str, 
                       help='要分析的实验名称 (experiment1, experiment2, etc.)')
    parser.add_argument('--list', '-l', action='store_true',
                       help='列出所有可用的实验')
    parser.add_argument('--timestamps', '-t', nargs='+',
                       help='指定要分析的时间戳')
    parser.add_argument('--output-dir', '-o', type=str,
                       default='VeryFL-PoL-main/experiments/analysis',
                       help='输出目录')
    parser.add_argument('--plots', '-p', action='store_true',
                       help='生成图表')
    
    args = parser.parse_args()
    
    analyzer = ExperimentAnalyzer()
    
    if args.list:
        experiments = analyzer.list_experiments()
        print("\n📊 可用的实验:")
        print("=" * 50)
        for exp_name, timestamps in experiments.items():
            print(f"{exp_name}: {len(timestamps)} 次运行")
            for ts in timestamps[-3:]:  # 显示最近3次
                print(f"  - {ts}")
            if len(timestamps) > 3:
                print(f"  ... 还有 {len(timestamps) - 3} 次运行")
        return
    
    if not args.experiment:
        print("❌ 请指定要分析的实验名称，或使用 --list 查看可用实验")
        return
    
    # 分析实验
    analysis = analyzer.analyze_experiment(args.experiment, args.timestamps)
    if not analysis:
        return
    
    # 生成报告
    report_file = analyzer.generate_report(args.experiment, analysis)
    print(f"✅ 分析报告已生成: {report_file}")
    
    # 生成图表
    if args.plots:
        plot_files = analyzer.generate_plots(args.experiment, analysis)
        print(f"✅ 图表已生成: {len(plot_files)} 个文件")
        for plot_file in plot_files:
            print(f"   - {plot_file}")
    
    # 打印简要结果
    print(f"\n📊 {args.experiment} 分析结果:")
    print("=" * 50)
    print(f"总运行次数: {analysis['total_runs']}")
    
    for metric, stats in analysis['statistics'].items():
        consistency = analysis['consistency'].get(metric, {})
        level = consistency.get('consistency_level', 'unknown')
        print(f"{metric}: {stats['mean']:.4f} ± {stats['std']:.4f} ({level})")

if __name__ == "__main__":
    main()
