#!/usr/bin/env python3
"""
VeryFL-PoL 统一实验管理器
整合所有实验功能，提供批量实验和高级实验功能

使用方法:
    python experiments.py suite --name quick
    python experiments.py batch --config batch_config.yaml
    python experiments.py network --clients 10,20,30
    python experiments.py compression --methods all
"""

import os
import sys
import yaml
import json
import time
import logging
import argparse
from typing import Dict, List, Any, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass, asdict

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入核心模块（延迟导入避免循环依赖）
def get_runner_and_gpu():
    try:
        from run import VeryFLRunner
        from experiments.gpu_manager import gpu_manager, get_gpu_status
        return VeryFLRunner, get_gpu_status
    except ImportError:
        # 如果导入失败，使用简化版本
        from run import Very<PERSON><PERSON>un<PERSON>
        def simple_get_status():
            import torch
            if torch.cuda.is_available():
                return f"🚀 检测到 {torch.cuda.device_count()} 个GPU"
            else:
                return "🔧 CPU模式"
        return VeryFLRunner, simple_get_status

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class ExperimentResult:
    """实验结果数据类"""
    name: str
    success: bool
    final_accuracy: float
    convergence_rounds: int
    total_time: float
    communication_cost: float
    storage_cost: float
    device_used: str
    error_message: Optional[str] = None
    metrics: Optional[Dict[str, Any]] = None

class ExperimentManager:
    """统一实验管理器"""
    
    def __init__(self, config_file: str = "config.yaml"):
        self.config_file = config_file
        self.config = self._load_config()

        # 获取运行器和GPU状态函数
        VeryFLRunner, self.get_gpu_status = get_runner_and_gpu()
        self.runner = VeryFLRunner()

        self.results_dir = self.config.get('output', {}).get('results_dir', 'results')
        os.makedirs(self.results_dir, exist_ok=True)

        logger.info("🚀 实验管理器初始化完成")
        logger.info(self.get_gpu_status())
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                logger.info(f"✅ 已加载配置文件: {self.config_file}")
                return config
        except Exception as e:
            logger.error(f"❌ 配置文件加载失败: {e}")
            return {}
    
    def run_experiment_suite(self, suite_name: str) -> List[ExperimentResult]:
        """运行预定义的实验套件"""
        logger.info(f"🎯 开始运行实验套件: {suite_name}")
        
        suites = self.config.get('experiment_suites', {})
        if suite_name not in suites:
            available = list(suites.keys())
            raise ValueError(f"未知的实验套件: {suite_name}，可用套件: {available}")
        
        suite_config = suites[suite_name]
        results = []
        
        for exp_config in suite_config:
            try:
                logger.info(f"🔄 运行实验: {exp_config['name']}")
                
                # 转换配置为参数对象
                args = self._config_to_args(exp_config)
                
                # 运行实验
                if exp_config.get('attack_type'):
                    exp_results = self.runner.run_attack_experiment(args)
                else:
                    exp_results = self.runner.run_basic_experiment(args)
                
                # 记录结果
                result = ExperimentResult(
                    name=exp_config['name'],
                    success=True,
                    final_accuracy=exp_results.get('final_accuracy', 0.0),
                    convergence_rounds=exp_results.get('convergence_rounds', exp_config['rounds']),
                    total_time=exp_results.get('total_time', 0.0),
                    communication_cost=exp_results.get('communication_cost', 0.0),
                    storage_cost=exp_results.get('storage_cost', 0.0),
                    device_used=exp_results.get('device_used', 'unknown'),
                    metrics=exp_results
                )
                results.append(result)
                
                logger.info(f"✅ 实验 {exp_config['name']} 完成: 准确率 {result.final_accuracy:.4f}")
                
            except Exception as e:
                logger.error(f"❌ 实验 {exp_config['name']} 失败: {e}")
                result = ExperimentResult(
                    name=exp_config['name'],
                    success=False,
                    final_accuracy=0.0,
                    convergence_rounds=exp_config['rounds'],
                    total_time=0.0,
                    communication_cost=0.0,
                    storage_cost=0.0,
                    device_used='unknown',
                    error_message=str(e)
                )
                results.append(result)
        
        # 保存套件结果
        self._save_suite_results(suite_name, results)
        
        # 生成报告
        self._generate_suite_report(suite_name, results)
        
        logger.info(f"🎉 实验套件 {suite_name} 完成")
        return results
    
    def run_parallel_experiments(self, experiments: List[Dict[str, Any]], max_parallel: int = 2) -> List[ExperimentResult]:
        """并行运行多个实验"""
        logger.info(f"🚀 开始并行实验，共 {len(experiments)} 个实验，并行度: {max_parallel}")
        
        results = []
        
        def run_single_experiment(exp_config):
            """运行单个实验的包装函数"""
            try:
                args = self._config_to_args(exp_config)
                
                if exp_config.get('attack_type'):
                    exp_results = self.runner.run_attack_experiment(args)
                else:
                    exp_results = self.runner.run_basic_experiment(args)
                
                return ExperimentResult(
                    name=exp_config['name'],
                    success=True,
                    final_accuracy=exp_results.get('final_accuracy', 0.0),
                    convergence_rounds=exp_results.get('convergence_rounds', exp_config.get('rounds', 5)),
                    total_time=exp_results.get('total_time', 0.0),
                    communication_cost=exp_results.get('communication_cost', 0.0),
                    storage_cost=exp_results.get('storage_cost', 0.0),
                    device_used=exp_results.get('device_used', 'unknown'),
                    metrics=exp_results
                )
                
            except Exception as e:
                logger.error(f"❌ 实验 {exp_config['name']} 失败: {e}")
                return ExperimentResult(
                    name=exp_config['name'],
                    success=False,
                    final_accuracy=0.0,
                    convergence_rounds=exp_config.get('rounds', 5),
                    total_time=0.0,
                    communication_cost=0.0,
                    storage_cost=0.0,
                    device_used='unknown',
                    error_message=str(e)
                )
        
        # 并行执行
        with ThreadPoolExecutor(max_workers=max_parallel) as executor:
            future_to_config = {
                executor.submit(run_single_experiment, config): config 
                for config in experiments
            }
            
            for future in as_completed(future_to_config):
                result = future.result()
                results.append(result)
                
                # 定期显示GPU状态
                if len(results) % 2 == 0:
                    logger.info(f"📊 进度: {len(results)}/{len(experiments)}")
                    logger.info(self.get_gpu_status())
        
        return results
    
    def run_network_experiments(self, client_nums: List[int], network_conditions: List[str] = None) -> List[ExperimentResult]:
        """运行网络可扩展性实验"""
        logger.info(f"🌐 开始网络实验: 客户端数量 {client_nums}")
        
        if network_conditions is None:
            network_conditions = ['ideal']
        
        experiments = []
        for client_num in client_nums:
            for condition in network_conditions:
                exp_config = {
                    'name': f'network_{client_num}c_{condition}',
                    'dataset': 'FashionMNIST',
                    'rounds': 5,
                    'client_num': client_num,
                    'enable_pol': True,
                    'network_condition': condition
                }
                experiments.append(exp_config)
        
        return self.run_parallel_experiments(experiments, max_parallel=2)
    
    def run_compression_experiments(self, methods: List[str] = None) -> List[ExperimentResult]:
        """运行压缩效果实验"""
        logger.info("🗜️ 开始压缩实验")
        
        if methods is None or 'all' in methods:
            methods = self.config.get('compression_methods', ['none', 'quantization', 'incremental'])
        
        experiments = []
        for method in methods:
            exp_config = {
                'name': f'compression_{method}',
                'dataset': 'FashionMNIST',
                'rounds': 5,
                'client_num': 10,
                'enable_pol': True,
                'compression_method': method
            }
            experiments.append(exp_config)
        
        return self.run_parallel_experiments(experiments, max_parallel=2)
    
    def _config_to_args(self, config: Dict[str, Any]):
        """将配置字典转换为参数对象"""
        class Args:
            pass
        
        args = Args()
        args.dataset = config.get('dataset', 'FashionMNIST')
        args.rounds = config.get('rounds', 5)
        args.client_num = config.get('client_num', 10)
        args.model = config.get('model', 'simpleCNN')
        args.enable_pol = config.get('enable_pol', True)
        args.type = config.get('attack_type')
        args.ratio = config.get('malicious_ratio', 0.1)
        args.methods = config.get('methods', 'pol')
        
        return args
    
    def _save_suite_results(self, suite_name: str, results: List[ExperimentResult]):
        """保存套件结果"""
        timestamp = int(time.time())
        results_file = os.path.join(self.results_dir, f"{suite_name}_results_{timestamp}.json")
        
        results_data = [asdict(result) for result in results]
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📁 结果已保存: {results_file}")
    
    def _generate_suite_report(self, suite_name: str, results: List[ExperimentResult]):
        """生成套件报告"""
        successful = sum(1 for r in results if r.success)
        total = len(results)
        avg_accuracy = sum(r.final_accuracy for r in results if r.success) / max(successful, 1)
        
        report = {
            'suite_name': suite_name,
            'total_experiments': total,
            'successful_experiments': successful,
            'success_rate': successful / total if total > 0 else 0,
            'average_accuracy': avg_accuracy,
            'timestamp': time.time(),
            'gpu_status': self.get_gpu_status()
        }
        
        timestamp = int(time.time())
        report_file = os.path.join(self.results_dir, f"{suite_name}_report_{timestamp}.json")
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📊 报告已生成: {report_file}")
        logger.info(f"📈 套件统计: 成功率 {successful}/{total} ({successful/total*100:.1f}%), 平均准确率 {avg_accuracy:.4f}")

def create_parser():
    """创建命令行解析器"""
    parser = argparse.ArgumentParser(
        description="VeryFL-PoL 统一实验管理器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python experiments.py suite --name quick
  python experiments.py network --clients 10,20,30
  python experiments.py compression --methods all
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='实验类型')
    
    # 套件实验
    suite_parser = subparsers.add_parser('suite', help='运行预定义实验套件')
    suite_parser.add_argument('--name', required=True,
                             choices=['quick', 'performance', 'attack_defense', 'compression'],
                             help='套件名称')
    
    # 网络实验
    network_parser = subparsers.add_parser('network', help='网络可扩展性实验')
    network_parser.add_argument('--clients', required=True,
                               help='客户端数量，逗号分隔 (如: 10,20,30)')
    network_parser.add_argument('--conditions', 
                               help='网络条件，逗号分隔 (如: ideal,high_latency)')
    
    # 压缩实验
    compression_parser = subparsers.add_parser('compression', help='压缩效果实验')
    compression_parser.add_argument('--methods', default='all',
                                   help='压缩方法，逗号分隔 (如: none,quantization,incremental)')
    
    return parser

def main():
    """主函数"""
    parser = create_parser()
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # 创建实验管理器
    manager = ExperimentManager()
    
    try:
        # 根据命令执行相应实验
        if args.command == 'suite':
            results = manager.run_experiment_suite(args.name)
        elif args.command == 'network':
            client_nums = [int(x.strip()) for x in args.clients.split(',')]
            conditions = None
            if args.conditions:
                conditions = [x.strip() for x in args.conditions.split(',')]
            results = manager.run_network_experiments(client_nums, conditions)
        elif args.command == 'compression':
            methods = [x.strip() for x in args.methods.split(',')]
            results = manager.run_compression_experiments(methods)
        else:
            logger.error(f"❌ 未知命令: {args.command}")
            return
        
        logger.info("🎉 实验管理器执行完成！")
        
    except KeyboardInterrupt:
        logger.info("⚠️ 用户中断实验")
    except Exception as e:
        logger.error(f"❌ 实验管理器执行失败: {e}")
        raise

if __name__ == "__main__":
    main()
