#!/usr/bin/env python3
"""
VeryFL-PoL 统一实验入口
简化所有实验启动方式，提供统一的命令行接口

使用方法:
    python run.py basic --dataset FashionMNIST --rounds 5
    python run.py attack --type model_poison --ratio 0.1
    python run.py comparison --methods pol,fedavg,krum
    python run.py quick-test
"""

import os
import sys
import argparse
import logging
import yaml
from typing import Dict, List, Any, Optional

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入核心模块（保持不变）
from task import Task
import config.benchmark

# GPU管理器导入（延迟导入避免循环依赖）
def get_gpu_functions():
    try:
        from experiments.gpu_manager import assign_gpu_for_experiment, release_gpu_for_experiment, get_gpu_status
        return assign_gpu_for_experiment, release_gpu_for_experiment, get_gpu_status
    except ImportError:
        # 如果GPU管理器不可用，使用简单的替代函数
        def simple_assign_gpu(exp_id, client_num=10, model="simpleCNN"):
            import torch
            return 'cuda' if torch.cuda.is_available() else 'cpu'

        def simple_release_gpu(exp_id):
            pass

        def simple_get_status():
            import torch
            if torch.cuda.is_available():
                return f"🚀 检测到 {torch.cuda.device_count()} 个GPU"
            else:
                return "🔧 CPU模式"

        return simple_assign_gpu, simple_release_gpu, simple_get_status

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class VeryFLRunner:
    """VeryFL-PoL统一运行器"""
    
    def __init__(self):
        self.config_file = "config.yaml"
        self.results_dir = "results"
        os.makedirs(self.results_dir, exist_ok=True)

        # 获取GPU管理函数
        self.assign_gpu, self.release_gpu, self.get_gpu_status = get_gpu_functions()

        # 加载默认配置
        self.default_config = self._load_default_config()

        logger.info("🚀 VeryFL-PoL统一运行器初始化完成")
        logger.info(self.get_gpu_status())
    
    def _load_default_config(self) -> Dict[str, Any]:
        """加载默认配置"""
        default_config = {
            'datasets': ['FashionMNIST', 'CIFAR10', 'CIFAR100'],
            'models': ['SimpleCNN', 'resnet18', 'resnet34'],
            'client_nums': [5, 10, 20],
            'communication_rounds': [3, 5, 10],
            'attack_types': ['free_rider', 'model_poison', 'data_poison'],
            'malicious_ratios': [0.1, 0.2, 0.3],
            'compression_methods': ['none', 'quantization', 'incremental'],
            'network_conditions': ['ideal', 'high_latency', 'low_bandwidth']
        }
        
        # 如果存在配置文件，加载并合并
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    user_config = yaml.safe_load(f)
                    if user_config:
                        default_config.update(user_config)
                        logger.info(f"✅ 已加载配置文件: {self.config_file}")
            except Exception as e:
                logger.warning(f"⚠️ 配置文件加载失败: {e}，使用默认配置")
        
        return default_config
    
    def run_basic_experiment(self, args) -> Dict[str, Any]:
        """运行基础联邦学习实验"""
        logger.info(f"🎯 开始基础实验: {args.dataset}")
        
        # 生成实验ID
        experiment_id = f"basic_{args.dataset}_{args.rounds}r"
        
        try:
            # 智能分配GPU
            device = self.assign_gpu(experiment_id, args.client_num, args.model)
            
            # 获取基准配置
            benchmark_name = f"PoL{args.dataset}" if args.enable_pol else args.dataset
            benchmark = config.benchmark.get_benchmark(benchmark_name)
            global_args, train_args, algorithm = benchmark.get_args()
            
            # 应用用户参数
            global_args.update({
                'communication_round': args.rounds,
                'client_num': args.client_num,
                'device': device,
                'enable_pol': args.enable_pol,
                'data_folder': os.path.join(self.results_dir, 'data')
            })
            
            if args.model:
                global_args['model'] = args.model
            
            logger.info(f"📊 实验配置: {args.dataset}, {args.rounds}轮, {args.client_num}客户端, 设备: {device}")
            
            # 运行实验
            task = Task(global_args=global_args, train_args=train_args, algorithm=algorithm)
            results = task.run()
            
            # 释放GPU资源
            self.release_gpu(experiment_id)
            
            logger.info(f"✅ 基础实验完成: 最终准确率 {results.get('final_accuracy', 0):.4f}")
            return results
            
        except Exception as e:
            logger.error(f"❌ 基础实验失败: {e}")
            try:
                self.release_gpu(experiment_id)
            except:
                pass
            raise
    
    def run_attack_experiment(self, args) -> Dict[str, Any]:
        """运行攻击防御实验"""
        logger.info(f"🛡️ 开始攻击实验: {args.type} (恶意比例: {args.ratio})")
        
        experiment_id = f"attack_{args.type}_{args.ratio}"
        
        try:
            # 智能分配GPU
            device = self.assign_gpu(experiment_id, args.client_num)
            
            # 获取PoL基准配置（攻击实验需要PoL防御）
            benchmark = config.benchmark.get_benchmark("PoLFashionMNIST")
            global_args, train_args, algorithm = benchmark.get_args()
            
            # 配置攻击参数
            global_args.update({
                'communication_round': args.rounds,
                'client_num': args.client_num,
                'device': device,
                'attack_type': args.type,
                'malicious_ratio': args.ratio,
                'enable_pol': True,  # 攻击实验必须启用PoL
                'data_folder': os.path.join(self.results_dir, 'data')
            })
            
            logger.info(f"🎯 攻击实验配置: {args.type}, 恶意比例 {args.ratio}, 设备: {device}")
            
            # 运行实验
            task = Task(global_args=global_args, train_args=train_args, algorithm=algorithm)
            results = task.run()
            
            # 释放GPU资源
            self.release_gpu(experiment_id)

            logger.info(f"✅ 攻击实验完成: 检测率 {results.get('detection_rate', 0):.4f}")
            return results

        except Exception as e:
            logger.error(f"❌ 攻击实验失败: {e}")
            try:
                self.release_gpu(experiment_id)
            except:
                pass
            raise
    
    def run_comparison_experiment(self, args) -> Dict[str, Any]:
        """运行方法对比实验"""
        logger.info(f"📊 开始对比实验: {args.methods}")
        
        methods = args.methods.split(',')
        results = {}
        
        for method in methods:
            method = method.strip()
            experiment_id = f"comparison_{method}"
            
            try:
                # 智能分配GPU
                device = self.assign_gpu(experiment_id, args.client_num)
                
                # 根据方法选择基准配置
                if method == 'pol':
                    benchmark = config.benchmark.get_benchmark("PoLFashionMNIST")
                else:
                    benchmark = config.benchmark.get_benchmark("FashionMNIST")
                
                global_args, train_args, algorithm = benchmark.get_args()
                
                # 配置实验参数
                global_args.update({
                    'communication_round': args.rounds,
                    'client_num': args.client_num,
                    'device': device,
                    'enable_pol': (method == 'pol'),
                    'data_folder': os.path.join(self.results_dir, 'data')
                })
                
                logger.info(f"🔄 运行方法: {method}, 设备: {device}")
                
                # 运行实验
                task = Task(global_args=global_args, train_args=train_args, algorithm=algorithm)
                method_results = task.run()
                results[method] = method_results
                
                # 释放GPU资源
                self.release_gpu(experiment_id)

                logger.info(f"✅ 方法 {method} 完成: 准确率 {method_results.get('final_accuracy', 0):.4f}")

            except Exception as e:
                logger.error(f"❌ 方法 {method} 失败: {e}")
                try:
                    self.release_gpu(experiment_id)
                except:
                    pass
                results[method] = {'error': str(e), 'final_accuracy': 0.0}
        
        logger.info("📊 对比实验完成")
        return results
    
    def run_quick_test(self) -> Dict[str, Any]:
        """运行快速测试"""
        logger.info("⚡ 开始快速测试")
        
        # 快速测试配置：小规模、少轮次
        class QuickTestArgs:
            dataset = "FashionMNIST"
            rounds = 2
            client_num = 5
            model = "simpleCNN"  # 修正模型名称，ModelFactory期望小写
            enable_pol = True
        
        return self.run_basic_experiment(QuickTestArgs())

def create_parser():
    """创建命令行解析器"""
    parser = argparse.ArgumentParser(
        description="VeryFL-PoL 统一实验入口",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python run.py basic --dataset FashionMNIST --rounds 5
  python run.py attack --type model_poison --ratio 0.1
  python run.py comparison --methods pol,fedavg,krum
  python run.py quick-test
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='实验类型')
    
    # 基础实验
    basic_parser = subparsers.add_parser('basic', help='基础联邦学习实验')
    basic_parser.add_argument('--dataset', default='FashionMNIST', 
                             choices=['FashionMNIST', 'CIFAR10', 'CIFAR100'],
                             help='数据集')
    basic_parser.add_argument('--rounds', type=int, default=5, help='通信轮数')
    basic_parser.add_argument('--client-num', type=int, default=10, help='客户端数量')
    basic_parser.add_argument('--model', help='模型类型')
    basic_parser.add_argument('--enable-pol', action='store_true', help='启用PoL验证')
    
    # 攻击实验
    attack_parser = subparsers.add_parser('attack', help='攻击防御实验')
    attack_parser.add_argument('--type', required=True,
                              choices=['free_rider', 'model_poison', 'data_poison'],
                              help='攻击类型')
    attack_parser.add_argument('--ratio', type=float, default=0.1, help='恶意客户端比例')
    attack_parser.add_argument('--rounds', type=int, default=5, help='通信轮数')
    attack_parser.add_argument('--client-num', type=int, default=10, help='客户端数量')
    
    # 对比实验
    comparison_parser = subparsers.add_parser('comparison', help='方法对比实验')
    comparison_parser.add_argument('--methods', required=True,
                                  help='对比方法，逗号分隔 (如: pol,fedavg,krum)')
    comparison_parser.add_argument('--rounds', type=int, default=5, help='通信轮数')
    comparison_parser.add_argument('--client-num', type=int, default=10, help='客户端数量')
    
    # 快速测试
    subparsers.add_parser('quick-test', help='快速测试（小规模验证）')
    
    return parser

def main():
    """主函数"""
    parser = create_parser()
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # 创建运行器
    runner = VeryFLRunner()
    
    try:
        # 根据命令执行相应实验
        if args.command == 'basic':
            results = runner.run_basic_experiment(args)
        elif args.command == 'attack':
            results = runner.run_attack_experiment(args)
        elif args.command == 'comparison':
            results = runner.run_comparison_experiment(args)
        elif args.command == 'quick-test':
            results = runner.run_quick_test()
        else:
            logger.error(f"❌ 未知命令: {args.command}")
            return
        
        logger.info("🎉 实验执行完成！")
        
    except KeyboardInterrupt:
        logger.info("⚠️ 用户中断实验")
    except Exception as e:
        logger.error(f"❌ 实验执行失败: {e}")
        raise

if __name__ == "__main__":
    main()
