#!/usr/bin/env python3
"""
VeryFL-PoL 简单易用的实验入口
让联邦学习实验变得简单直观

🚀 快速开始:
    python run.py                    # 交互式模式，跟着提示走
    python run.py demo               # 演示模式，看看所有功能
    python run.py test               # 基础测试，验证系统工作

📊 常用场景:
    python run.py compare            # 对比PoL和传统方法
    python run.py attack             # 测试攻击防御能力
    python run.py scale              # 测试可扩展性

📚 论文实验:
    python run.py paper              # 运行论文中的所有实验
    python run.py benchmark          # 标准基准测试
"""

import os
import sys
import argparse
import logging
import yaml
from typing import Dict, List, Any, Optional

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 确保实验可重现性
from util.reproducibility import ensure_reproducible_environment
ensure_reproducible_environment()

# 导入核心模块（保持不变）
from task import Task
import config.benchmark

# GPU管理器导入（延迟导入避免循环依赖）
def get_gpu_functions():
    try:
        from experiments.gpu_manager import assign_gpu_for_experiment, release_gpu_for_experiment, get_gpu_status
        return assign_gpu_for_experiment, release_gpu_for_experiment, get_gpu_status
    except ImportError:
        # 如果GPU管理器不可用，使用简单的替代函数
        def simple_assign_gpu(exp_id, client_num=10, model="simpleCNN"):
            import torch
            return 'cuda' if torch.cuda.is_available() else 'cpu'

        def simple_release_gpu(exp_id):
            pass

        def simple_get_status():
            import torch
            if torch.cuda.is_available():
                return f"🚀 检测到 {torch.cuda.device_count()} 个GPU"
            else:
                return "🔧 CPU模式"

        return simple_assign_gpu, simple_release_gpu, simple_get_status

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class VeryFLRunner:
    """VeryFL-PoL简单易用的运行器"""

    def __init__(self):
        self.config_file = "config.yaml"
        self.results_dir = "results"
        os.makedirs(self.results_dir, exist_ok=True)

        # 获取GPU管理函数
        self.assign_gpu, self.release_gpu, self.get_gpu_status = get_gpu_functions()

        # 加载默认配置
        self.default_config = self._load_default_config()

        logger.info("🚀 VeryFL-PoL运行器初始化完成")
        logger.info(self.get_gpu_status())

        # 应用性能优化
        try:
            from performance_optimizer import optimize_for_hardware
            self.performance_config = optimize_for_hardware()
        except Exception as e:
            logger.warning(f"性能优化失败: {e}")
            self.performance_config = None

    def interactive_mode(self):
        """交互式模式 - 引导用户选择实验"""
        print("\n🎯 欢迎使用VeryFL-PoL！")
        print("让我帮你选择合适的实验...")
        print()

        print("选择要运行的实验：")
        print("=" * 50)
        print("🔬 VeryFL-PoL 实验框架")
        print("1. 📊 实验1：基础性能对比")
        print("2. 🛡️ 实验2：攻击防御效果")
        print("3. 🗜️ 实验3：压缩效果分析")
        print("4. 📈 实验4：可扩展性测试")
        print("5. 🧪 实验5：消融实验")
        print("6. 🎯 运行完整的五个实验板块")
        print()
        print("💡 提示：每次实验结果都会保存到独立的时间戳目录")
        print("💡 使用 python analyze_results.py 分析多次实验结果")
        print()

        try:
            choice = input("请选择 (1-6): ").strip()

            if choice == '1':
                return self.run_experiment_1()
            elif choice == '2':
                return self.run_experiment_2()
            elif choice == '3':
                return self.run_experiment_3()
            elif choice == '4':
                return self.run_experiment_4()
            elif choice == '5':
                return self.run_experiment_5()
            elif choice == '6':
                return self.run_all_experiments()
            else:
                print("❌ 无效选择，运行实验1...")
                return self.run_experiment_1()

        except KeyboardInterrupt:
            print("\n👋 再见！")
            return None
    
    def _load_default_config(self) -> Dict[str, Any]:
        """加载默认配置"""
        default_config = {
            'datasets': ['FashionMNIST', 'CIFAR10', 'CIFAR100'],
            'models': ['SimpleCNN', 'resnet18', 'resnet34'],
            'client_nums': [5, 10, 20],
            'communication_rounds': [3, 5, 10],
            'attack_types': ['free_rider', 'model_poison', 'data_poison'],
            'malicious_ratios': [0.1, 0.2, 0.3],
            'compression_methods': ['none', 'quantization', 'incremental'],
            'network_conditions': ['ideal', 'high_latency', 'low_bandwidth']
        }
        
        # 如果存在配置文件，加载并合并
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    user_config = yaml.safe_load(f)
                    if user_config:
                        default_config.update(user_config)
                        logger.info(f"✅ 已加载配置文件: {self.config_file}")
            except Exception as e:
                logger.warning(f"⚠️ 配置文件加载失败: {e}，使用默认配置")
        
        return default_config







    # ==================== 原始实验框架（五个实验板块） ====================

    def run_experiment_1(self):
        """实验1：基础性能对比"""
        from datetime import datetime
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        print("\n📊 实验1：基础性能对比")
        print("目标：对比PoL与传统联邦学习在多个数据集上的性能")
        print(f"结果将保存到：experiments/results/experiment1/{timestamp}/")
        print("预计时间：30-60分钟")
        print()

        return self._run_original_parallel_experiment("performance", "experiment1", timestamp)

    def run_experiment_2(self):
        """实验2：攻击防御效果"""
        from datetime import datetime
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        print("\n🛡️ 实验2：攻击防御效果")
        print("目标：验证PoL对各种攻击的防御能力")
        print(f"结果将保存到：experiments/results/experiment2/{timestamp}/")
        print("预计时间：60-120分钟")
        print()

        return self._run_original_parallel_experiment("attack_defense", "experiment2", timestamp)

    def run_experiment_3(self):
        """实验3：压缩效果分析"""
        from datetime import datetime
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        print("\n🗜️ 实验3：压缩效果分析")
        print("目标：分析PoL证明的压缩效果和存储开销")
        print(f"结果将保存到：experiments/results/experiment3/{timestamp}/")
        print("预计时间：45-90分钟")
        print()

        return self._run_original_parallel_experiment("compression", "experiment3", timestamp)

    def run_experiment_4(self):
        """实验4：可扩展性测试"""
        from datetime import datetime
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        print("\n📈 实验4：可扩展性测试")
        print("目标：测试PoL在不同网络规模下的可扩展性")
        print(f"结果将保存到：experiments/results/experiment4/{timestamp}/")
        print("预计时间：60-120分钟")
        print()

        return self._run_original_network_simulation("experiment4", timestamp)

    def run_experiment_5(self):
        """实验5：消融实验"""
        from datetime import datetime
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        print("\n🧪 实验5：消融实验")
        print("目标：分析PoL各个组件的贡献度")
        print(f"结果将保存到：experiments/results/experiment5/{timestamp}/")
        print("预计时间：45-90分钟")
        print()

        return self._run_original_ablation_study("experiment5", timestamp)

    def run_all_experiments(self):
        """运行完整的五个实验板块"""
        from datetime import datetime
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        print("\n🎯 运行完整的实验框架（五个板块）")
        print(f"结果将保存到：experiments/results/all_experiments/{timestamp}/")
        print("总预计时间：4-8小时")
        print()

        confirm = input("这将运行完整的实验框架，确定继续吗？(y/N): ").strip().lower()
        if confirm != 'y':
            print("已取消实验")
            return None

        return self._run_original_master_experiment("all_experiments", timestamp)

    def _detect_optimal_parallelism(self):
        """智能检测最优并行度"""
        try:
            import torch
            import psutil
            import os

            # 检测GPU
            gpu_count = 0
            gpu_memory_per_gpu = 0
            if torch.cuda.is_available():
                gpu_count = torch.cuda.device_count()
                gpu_memory_per_gpu = torch.cuda.get_device_properties(0).total_memory / (1024**3)  # GB
                print(f"🎮 检测到 {gpu_count} 个GPU，每个 {gpu_memory_per_gpu:.1f}GB VRAM")

            # 检测CPU
            cpu_cores = psutil.cpu_count(logical=False)  # 物理核心
            cpu_threads = psutil.cpu_count(logical=True)  # 逻辑线程
            print(f"🖥️ 检测到 {cpu_cores} 个CPU核心，{cpu_threads} 个线程")

            # 检测内存
            memory_gb = psutil.virtual_memory().total / (1024**3)
            print(f"💾 检测到 {memory_gb:.1f}GB 系统内存")

            # 智能计算最优并行度
            if gpu_count >= 2:
                # 双GPU或多GPU：基于GPU内存和数量
                if gpu_memory_per_gpu >= 20:  # RTX 4090级别
                    # 每个GPU可以并行2-3个实验
                    optimal = min(gpu_count * 2, 8)  # 最多8个并行
                    print(f"🚀 双GPU高端配置：推荐 {optimal} 并行")
                else:
                    # 中端GPU
                    optimal = gpu_count
                    print(f"🚀 多GPU中端配置：推荐 {optimal} 并行")
            elif gpu_count == 1:
                # 单GPU：基于GPU内存
                if gpu_memory_per_gpu >= 20:
                    optimal = 3  # RTX 4090可以并行3个
                    print(f"🚀 单GPU高端配置：推荐 {optimal} 并行")
                elif gpu_memory_per_gpu >= 10:
                    optimal = 2  # RTX 3080级别
                    print(f"🚀 单GPU中端配置：推荐 {optimal} 并行")
                else:
                    optimal = 1
                    print(f"🚀 单GPU入门配置：推荐 {optimal} 并行")
            else:
                # 纯CPU：基于CPU核心数和内存
                if memory_gb >= 32 and cpu_cores >= 8:
                    optimal = min(cpu_cores // 2, 6)  # 高端CPU配置
                    print(f"🚀 高端CPU配置：推荐 {optimal} 并行")
                elif memory_gb >= 16 and cpu_cores >= 4:
                    optimal = min(cpu_cores // 2, 4)  # 中端CPU配置
                    print(f"🚀 中端CPU配置：推荐 {optimal} 并行")
                else:
                    optimal = 2  # 保守配置
                    print(f"🚀 保守CPU配置：推荐 {optimal} 并行")

            # 安全检查：确保不超过合理范围
            optimal = max(1, min(optimal, 12))  # 1-12之间

            return optimal

        except Exception as e:
            print(f"⚠️ 硬件检测失败，使用默认并行度: {e}")
            return 4  # 默认值，比原来的2更激进



    def run_custom_mode(self):
        """自定义模式 - 让用户自己配置"""
        print("\n🎨 自定义模式：自己配置实验参数")
        print()

        try:
            # 数据集选择
            print("选择数据集：")
            print("1. FashionMNIST (推荐，快速)")
            print("2. CIFAR10 (中等复杂度)")
            print("3. CIFAR100 (复杂，较慢)")
            dataset_choice = input("选择 (1-3，默认1): ").strip() or "1"
            datasets = {"1": "FashionMNIST", "2": "CIFAR10", "3": "CIFAR100"}
            dataset = datasets.get(dataset_choice, "FashionMNIST")

            # 客户端数量
            client_num = input("客户端数量 (默认10): ").strip()
            client_num = int(client_num) if client_num.isdigit() else 10

            # 训练轮数
            rounds = input("训练轮数 (默认5): ").strip()
            rounds = int(rounds) if rounds.isdigit() else 5

            # 是否启用PoL
            enable_pol = input("启用PoL验证？(Y/n): ").strip().lower()
            enable_pol = enable_pol != 'n'

            print(f"\n📋 实验配置：")
            print(f"   数据集: {dataset}")
            print(f"   客户端: {client_num}")
            print(f"   轮数: {rounds}")
            print(f"   PoL: {'启用' if enable_pol else '禁用'}")
            print()

            class CustomArgs:
                pass

            args = CustomArgs()
            args.dataset = dataset
            args.client_num = client_num
            args.rounds = rounds
            args.model = "simpleCNN"
            args.enable_pol = enable_pol

            return self.run_basic_experiment(args)

        except KeyboardInterrupt:
            print("\n已取消")
            return None
        except Exception as e:
            print(f"❌ 配置错误: {e}")
            return None

    def _print_comparison_results(self, results):
        """打印对比结果"""
        print("\n" + "="*50)
        print("📊 对比结果")
        print("="*50)

        pol_acc = results.get('pol', {}).get('final_accuracy', 0)
        trad_acc = results.get('traditional', {}).get('final_accuracy', 0)

        print(f"PoL联邦学习准确率:     {pol_acc:.4f}")
        print(f"传统联邦学习准确率:     {trad_acc:.4f}")

        if pol_acc > trad_acc:
            improvement = ((pol_acc - trad_acc) / trad_acc) * 100
            print(f"✅ PoL提升了 {improvement:.2f}%")
        else:
            decline = ((trad_acc - pol_acc) / trad_acc) * 100
            print(f"⚠️ PoL下降了 {decline:.2f}%")

        print("="*50)

    def _print_scale_results(self, results):
        """打印扩展性结果"""
        print("\n" + "="*50)
        print("📈 扩展性测试结果")
        print("="*50)

        for key, result in results.items():
            client_num = key.replace('_clients', '')
            accuracy = result.get('final_accuracy', 0)
            time_cost = result.get('total_time', 0)
            print(f"{client_num:>2}个客户端: 准确率 {accuracy:.4f}, 用时 {time_cost:.1f}秒")

        print("="*50)

    def _analyze_performance_results(self, results):
        """分析性能实验结果"""
        print("\n" + "="*60)
        print("📊 性能证明实验结果分析")
        print("="*60)

        pol_acc = results.get('pol_fedavg', {}).get('final_accuracy', 0)
        trad_acc = results.get('traditional_fedavg', {}).get('final_accuracy', 0)

        print(f"实验组（PoL-FedAvg）最终准确率:    {pol_acc:.4f}")
        print(f"对照组（传统FedAvg）最终准确率:    {trad_acc:.4f}")

        if pol_acc > trad_acc:
            improvement = ((pol_acc - trad_acc) / trad_acc) * 100
            print(f"\n✅ 性能证明成功：PoL-FedAvg相对提升 {improvement:.2f}%")
            print("📈 结论：PoL机制能够提升联邦学习的模型性能")
        else:
            decline = ((trad_acc - pol_acc) / trad_acc) * 100
            print(f"\n⚠️ 性能未提升：PoL-FedAvg相对下降 {decline:.2f}%")
            print("📉 可能原因：训练轮数不足、数据集过简单、或需要调优")

        # 通信成本分析
        pol_cost = results.get('pol_fedavg', {}).get('communication_cost', 0)
        trad_cost = results.get('traditional_fedavg', {}).get('communication_cost', 0)

        if pol_cost > 0 and trad_cost > 0:
            cost_overhead = ((pol_cost - trad_cost) / trad_cost) * 100
            print(f"\n📡 通信开销：PoL增加 {cost_overhead:.2f}% 通信成本")
            if cost_overhead < 50:
                print("✅ 通信开销可接受（<50%）")
            else:
                print("⚠️ 通信开销较高，需要优化")

        print("="*60)

    def _analyze_security_results(self, results):
        """分析安全实验结果"""
        print("\n" + "="*60)
        print("🛡️ 安全证明实验结果分析")
        print("="*60)

        pol_result = results.get('pol_defense', {})
        detection_rate = pol_result.get('detection_rate', 0)
        final_acc = pol_result.get('final_accuracy', 0)

        print(f"恶意客户端检测率:    {detection_rate:.4f} ({detection_rate*100:.1f}%)")
        print(f"受保护模型准确率:    {final_acc:.4f}")

        if detection_rate > 0.8:
            print("\n✅ 安全证明成功：PoL能有效检测恶意客户端")
            print("🛡️ 结论：PoL机制具有强大的攻击防御能力")
        elif detection_rate > 0.5:
            print("\n⚠️ 安全防御中等：检测率需要进一步提升")
            print("🔧 建议：调整验证阈值或增加验证频率")
        else:
            print("\n❌ 安全防御不足：检测率过低")
            print("🚨 需要：重新设计验证策略或检查实现")

        print("="*60)

    def run_scalability_proof(self):
        """扩展证明 - 证明PoL具有良好的可扩展性"""
        print("\n📈 扩展证明实验：证明PoL机制的可扩展性")
        print("研究问题：PoL机制是否能在不同规模下保持良好性能？")
        print("实验设计：")
        print("  - 客户端规模：5, 10, 20客户端（小、中、大规模）")
        print("  - 评估指标：准确率稳定性、验证时间、通信开销")
        print("  - 固定配置：FashionMNIST，3轮训练")
        print("预计时间：25-30分钟")
        print()

        results = {}
        scales = [5, 10, 20]

        for client_num in scales:
            print(f"🔄 测试 {client_num} 客户端规模...")

            class ScaleArgs:
                dataset = "FashionMNIST"
                rounds = 3
                client_num = client_num
                model = "simpleCNN"
                enable_pol = True

            results[f'{client_num}_clients'] = self.run_basic_experiment(ScaleArgs())

        self._analyze_scalability_results(results)
        return results

    def run_complete_evaluation(self):
        """完整评估 - 获得发论文需要的所有实验数据"""
        print("\n📚 完整评估：运行所有关键实验获得完整数据")
        print("研究目标：获得发表论文所需的完整实验数据和图表")
        print("实验内容：")
        print("  1. 性能证明实验（PoL vs 传统FL）")
        print("  2. 安全证明实验（攻击防御能力）")
        print("  3. 扩展证明实验（可扩展性分析）")
        print("预计时间：60-90分钟")
        print()

        confirm = input("这将运行完整的实验套件，确定继续吗？(y/N): ").strip().lower()
        if confirm != 'y':
            print("已取消完整评估")
            return None

        all_results = {}

        print("\n" + "="*50)
        print("第1阶段：性能证明实验")
        print("="*50)
        all_results['performance'] = self.run_performance_proof()

        print("\n" + "="*50)
        print("第2阶段：安全证明实验")
        print("="*50)
        all_results['security'] = self.run_security_proof()

        print("\n" + "="*50)
        print("第3阶段：扩展证明实验")
        print("="*50)
        all_results['scalability'] = self.run_scalability_proof()

        # 生成完整报告
        self._generate_complete_report(all_results)
        return all_results

    def run_debug_mode(self):
        """调试模式 - 快速验证特定功能"""
        print("\n🎨 调试模式：快速验证特定功能")
        print("用于开发和调试，最小配置快速运行")
        print()

        return self.run_system_validation()  # 复用系统验证

    # ==================== 原始实验框架（五个实验板块） ====================

    def run_original_experiment_1(self):
        """原始实验1：基础性能对比"""
        print("\n📊 原始实验1：基础性能对比")
        print("这是原始实验框架的第一个板块")
        print("目标：对比PoL与传统联邦学习在多个数据集上的性能")
        print("实验设计：")
        print("  - 数据集：FashionMNIST, CIFAR10, CIFAR100")
        print("  - 对比方法：PoL-FedAvg vs 传统FedAvg")
        print("  - 评估指标：准确率、收敛速度、通信成本")
        print("预计时间：30-60分钟")
        print()

        # 调用原始的并行执行器
        return self._run_original_parallel_experiment("performance")

    def run_original_experiment_2(self):
        """原始实验2：攻击防御效果"""
        print("\n🛡️ 原始实验2：攻击防御效果")
        print("这是原始实验框架的第二个板块")
        print("目标：验证PoL对各种攻击的防御能力")
        print("实验设计：")
        print("  - 攻击类型：Free Rider, Partial Free Rider, Model Poisoning")
        print("  - 恶意比例：10%, 20%, 30%")
        print("  - 评估指标：攻击检测率、模型性能保护率")
        print("预计时间：60-120分钟")
        print()

        return self._run_original_parallel_experiment("attack_defense")

    def run_original_experiment_3(self):
        """原始实验3：压缩效果分析"""
        print("\n🗜️ 原始实验3：压缩效果分析")
        print("这是原始实验框架的第三个板块")
        print("目标：分析PoL证明的压缩效果和存储开销")
        print("实验设计：")
        print("  - 压缩方法：无压缩、量化、稀疏化")
        print("  - 模型规模：小型、中型、大型")
        print("  - 评估指标：压缩率、验证时间、存储开销")
        print("预计时间：45-90分钟")
        print()

        return self._run_original_parallel_experiment("compression")

    def run_original_experiment_4(self):
        """原始实验4：可扩展性测试"""
        print("\n📈 原始实验4：可扩展性测试")
        print("这是原始实验框架的第四个板块")
        print("目标：测试PoL在不同网络规模下的可扩展性")
        print("实验设计：")
        print("  - 客户端数量：10, 20, 50, 100")
        print("  - 网络条件：理想、高延迟、低带宽")
        print("  - 评估指标：验证时间、通信开销、系统吞吐量")
        print("预计时间：60-120分钟")
        print()

        return self._run_original_network_simulation()

    def run_original_experiment_5(self):
        """原始实验5：消融实验"""
        print("\n🧪 原始实验5：消融实验")
        print("这是原始实验框架的第五个板块")
        print("目标：分析PoL各个组件的贡献度")
        print("实验设计：")
        print("  - 消融组件：证明生成、验证策略、激励机制")
        print("  - 对比配置：完整PoL vs 各种简化版本")
        print("  - 评估指标：性能影响、安全性影响、效率影响")
        print("预计时间：45-90分钟")
        print()

        return self._run_original_ablation_study()

    def run_all_original_experiments(self):
        """运行完整的五个原始实验板块"""
        print("\n🎯 运行完整的原始实验框架（五个板块）")
        print("这将按顺序运行所有原始设计的实验")
        print("总预计时间：4-8小时")
        print()

        confirm = input("这将运行完整的原始实验框架，确定继续吗？(y/N): ").strip().lower()
        if confirm != 'y':
            print("已取消原始实验框架")
            return None

        return self._run_original_master_experiment()

    # ==================== 原始实验系统调用方法 ====================

    def _run_original_parallel_experiment(self, suite_name, experiment_name, timestamp):
        """调用原始的并行执行器，支持增量式存储"""
        try:
            import subprocess
            import sys
            import os

            print(f"🔄 启动原始并行执行器：{suite_name} 套件...")

            # 创建增量式结果目录
            result_dir = f"experiments/results/{experiment_name}/{timestamp}"
            os.makedirs(result_dir, exist_ok=True)

            # 智能检测最优并行度
            optimal_parallel = self._detect_optimal_parallelism()
            print(f"🚀 检测到最优并行度：{optimal_parallel}")

            # 切换到experiments目录并运行并行执行器
            cmd = [
                sys.executable,
                "parallel_executor.py",
                "--suite", suite_name,
                "--max-parallel", str(optimal_parallel),
                "--output-dir", f"results/{experiment_name}/{timestamp}"
            ]

            result = subprocess.run(
                cmd,
                cwd="experiments",
                capture_output=True,
                text=True,
                timeout=3600  # 1小时超时
            )

            # 保存实验元数据
            metadata = {
                "experiment_name": experiment_name,
                "suite_name": suite_name,
                "timestamp": timestamp,
                "success": result.returncode == 0,
                "command": " ".join(cmd),
                "stdout": result.stdout,
                "stderr": result.stderr
            }

            import json
            with open(f"{result_dir}/experiment_metadata.json", "w") as f:
                json.dump(metadata, f, indent=2)

            if result.returncode == 0:
                print("✅ 实验完成")
                print(f"📁 结果保存在: {result_dir}")
                return {"success": True, "output": result.stdout, "result_dir": result_dir}
            else:
                print("❌ 实验失败")
                print(f"错误信息: {result.stderr}")
                return {"success": False, "error": result.stderr, "result_dir": result_dir}

        except Exception as e:
            print(f"❌ 调用实验系统失败: {e}")
            return {"success": False, "error": str(e)}

    def _run_original_network_simulation(self, experiment_name, timestamp):
        """调用原始的网络模拟器，支持增量式存储"""
        try:
            import subprocess
            import sys
            import os

            print("🔄 启动原始网络模拟器...")

            # 创建增量式结果目录
            result_dir = f"experiments/results/{experiment_name}/{timestamp}"
            os.makedirs(result_dir, exist_ok=True)

            cmd = [
                sys.executable,
                "network_simulator.py",
                "--client-counts", "10", "20", "50",
                "--network-conditions", "ideal", "high_latency", "low_bandwidth",
                "--output-dir", f"results/{experiment_name}/{timestamp}"
            ]

            result = subprocess.run(
                cmd,
                cwd="experiments",
                capture_output=True,
                text=True,
                timeout=7200  # 2小时超时
            )

            # 保存实验元数据
            metadata = {
                "experiment_name": experiment_name,
                "timestamp": timestamp,
                "success": result.returncode == 0,
                "command": " ".join(cmd),
                "stdout": result.stdout,
                "stderr": result.stderr
            }

            import json
            with open(f"{result_dir}/experiment_metadata.json", "w") as f:
                json.dump(metadata, f, indent=2)

            if result.returncode == 0:
                print("✅ 网络可扩展性测试完成")
                print(f"📁 结果保存在: {result_dir}")
                return {"success": True, "output": result.stdout, "result_dir": result_dir}
            else:
                print("❌ 网络模拟测试失败")
                print(f"错误信息: {result.stderr}")
                return {"success": False, "error": result.stderr, "result_dir": result_dir}

        except Exception as e:
            print(f"❌ 调用网络模拟器失败: {e}")
            return {"success": False, "error": str(e)}

    def _run_original_ablation_study(self, experiment_name, timestamp):
        """调用原始的消融研究，支持增量式存储"""
        try:
            import subprocess
            import sys
            import os

            print("🔄 启动原始消融研究...")

            # 创建增量式结果目录
            result_dir = f"experiments/results/{experiment_name}/{timestamp}"
            os.makedirs(result_dir, exist_ok=True)

            cmd = [
                sys.executable,
                "ablation_study.py",
                "--datasets", "FashionMNIST", "CIFAR10",
                "--output-dir", f"results/{experiment_name}/{timestamp}"
            ]

            result = subprocess.run(
                cmd,
                cwd="experiments",
                capture_output=True,
                text=True,
                timeout=5400  # 1.5小时超时
            )

            # 保存实验元数据
            metadata = {
                "experiment_name": experiment_name,
                "timestamp": timestamp,
                "success": result.returncode == 0,
                "command": " ".join(cmd),
                "stdout": result.stdout,
                "stderr": result.stderr
            }

            import json
            with open(f"{result_dir}/experiment_metadata.json", "w") as f:
                json.dump(metadata, f, indent=2)

            if result.returncode == 0:
                print("✅ 消融研究完成")
                print(f"📁 结果保存在: {result_dir}")
                return {"success": True, "output": result.stdout, "result_dir": result_dir}
            else:
                print("❌ 消融研究失败")
                print(f"错误信息: {result.stderr}")
                return {"success": False, "error": result.stderr, "result_dir": result_dir}

        except Exception as e:
            print(f"❌ 调用消融研究失败: {e}")
            return {"success": False, "error": str(e)}

    def _run_original_master_experiment(self, experiment_name, timestamp):
        """调用原始的主实验控制器，支持增量式存储"""
        try:
            import subprocess
            import sys
            import os

            print("🔄 启动原始主实验控制器...")

            # 创建增量式结果目录
            result_dir = f"experiments/results/{experiment_name}/{timestamp}"
            os.makedirs(result_dir, exist_ok=True)

            cmd = [
                sys.executable,
                "master_experiment_runner.py",
                "--experiment", "all",
                "--output-dir", f"results/{experiment_name}/{timestamp}"
            ]

            result = subprocess.run(
                cmd,
                cwd="experiments",
                capture_output=True,
                text=True,
                timeout=28800  # 8小时超时
            )

            # 保存实验元数据
            metadata = {
                "experiment_name": experiment_name,
                "timestamp": timestamp,
                "success": result.returncode == 0,
                "command": " ".join(cmd),
                "stdout": result.stdout,
                "stderr": result.stderr
            }

            import json
            with open(f"{result_dir}/experiment_metadata.json", "w") as f:
                json.dump(metadata, f, indent=2)

            if result.returncode == 0:
                print("✅ 完整实验框架完成")
                print(f"📁 结果保存在: {result_dir}")
                return {"success": True, "output": result.stdout, "result_dir": result_dir}
            else:
                print("❌ 实验框架失败")
                print(f"错误信息: {result.stderr}")
                return {"success": False, "error": result.stderr, "result_dir": result_dir}

        except Exception as e:
            print(f"❌ 调用主实验控制器失败: {e}")
            return {"success": False, "error": str(e)}

    def _analyze_scalability_results(self, results):
        """分析可扩展性实验结果"""
        print("\n" + "="*60)
        print("📈 扩展证明实验结果分析")
        print("="*60)

        print("客户端规模 | 准确率   | 训练时间 | 验证时间")
        print("-" * 50)

        accuracies = []
        for key, result in results.items():
            client_num = key.replace('_clients', '')
            accuracy = result.get('final_accuracy', 0)
            train_time = result.get('total_time', 0)
            verify_time = result.get('verification_time', 0)

            print(f"{client_num:>8}个   | {accuracy:.4f}  | {train_time:>6.1f}s | {verify_time:>6.1f}s")
            accuracies.append(accuracy)

        # 稳定性分析
        if len(accuracies) >= 2:
            accuracy_std = sum(accuracies) / len(accuracies)
            accuracy_var = sum((x - accuracy_std)**2 for x in accuracies) / len(accuracies)

            print(f"\n📊 准确率稳定性分析:")
            print(f"   平均准确率: {accuracy_std:.4f}")
            print(f"   准确率方差: {accuracy_var:.6f}")

            if accuracy_var < 0.001:
                print("✅ 扩展证明成功：准确率在不同规模下保持稳定")
            else:
                print("⚠️ 准确率波动较大，可能需要调优")

        print("="*60)

    def _generate_complete_report(self, all_results):
        """生成完整的实验报告"""
        print("\n" + "="*70)
        print("📚 VeryFL-PoL 完整评估报告")
        print("="*70)

        # 性能总结
        perf_results = all_results.get('performance', {})
        if perf_results:
            pol_acc = perf_results.get('pol_fedavg', {}).get('final_accuracy', 0)
            trad_acc = perf_results.get('traditional_fedavg', {}).get('final_accuracy', 0)
            if pol_acc > trad_acc:
                improvement = ((pol_acc - trad_acc) / trad_acc) * 100
                print(f"✅ 性能优势：PoL相对传统FL提升 {improvement:.2f}%")
            else:
                print("⚠️ 性能优势：需要进一步优化")

        # 安全总结
        sec_results = all_results.get('security', {})
        if sec_results:
            detection_rate = sec_results.get('pol_defense', {}).get('detection_rate', 0)
            print(f"🛡️ 安全防御：恶意客户端检测率 {detection_rate*100:.1f}%")

        # 扩展总结
        scale_results = all_results.get('scalability', {})
        if scale_results:
            print(f"📈 可扩展性：支持 5-20 客户端规模")

        print("\n🎉 完整评估完成！所有实验数据已生成，可用于论文撰写。")
        print("="*70)
    
    def run_basic_experiment(self, args) -> Dict[str, Any]:
        """运行基础联邦学习实验"""
        logger.info(f"🎯 开始基础实验: {args.dataset}")
        
        # 生成实验ID
        experiment_id = f"basic_{args.dataset}_{args.rounds}r"
        
        try:
            # 智能分配GPU
            device = self.assign_gpu(experiment_id, args.client_num, args.model)
            
            # 获取基准配置
            benchmark_name = f"PoL{args.dataset}" if args.enable_pol else args.dataset
            benchmark = config.benchmark.get_benchmark(benchmark_name)
            global_args, train_args, algorithm = benchmark.get_args()
            
            # 应用用户参数
            global_args.update({
                'communication_round': args.rounds,
                'client_num': args.client_num,
                'device': device,
                'enable_pol': args.enable_pol,
                'data_folder': os.path.join(self.results_dir, 'data')
            })
            
            if args.model:
                global_args['model'] = args.model
            
            logger.info(f"📊 实验配置: {args.dataset}, {args.rounds}轮, {args.client_num}客户端, 设备: {device}")
            
            # 运行实验
            task = Task(global_args=global_args, train_args=train_args, algorithm=algorithm)
            results = task.run()
            
            # 释放GPU资源
            self.release_gpu(experiment_id)
            
            logger.info(f"✅ 基础实验完成: 最终准确率 {results.get('final_accuracy', 0):.4f}")
            return results
            
        except Exception as e:
            logger.error(f"❌ 基础实验失败: {e}")
            try:
                self.release_gpu(experiment_id)
            except:
                pass
            raise
    
    def run_attack_experiment(self, args) -> Dict[str, Any]:
        """运行攻击防御实验"""
        logger.info(f"🛡️ 开始攻击实验: {args.type} (恶意比例: {args.ratio})")
        
        experiment_id = f"attack_{args.type}_{args.ratio}"
        
        try:
            # 智能分配GPU
            device = self.assign_gpu(experiment_id, args.client_num)
            
            # 获取PoL基准配置（攻击实验需要PoL防御）
            benchmark = config.benchmark.get_benchmark("PoLFashionMNIST")
            global_args, train_args, algorithm = benchmark.get_args()
            
            # 配置攻击参数
            global_args.update({
                'communication_round': args.rounds,
                'client_num': args.client_num,
                'device': device,
                'attack_type': args.type,
                'malicious_ratio': args.ratio,
                'enable_pol': True,  # 攻击实验必须启用PoL
                'data_folder': os.path.join(self.results_dir, 'data')
            })
            
            logger.info(f"🎯 攻击实验配置: {args.type}, 恶意比例 {args.ratio}, 设备: {device}")
            
            # 运行实验
            task = Task(global_args=global_args, train_args=train_args, algorithm=algorithm)
            results = task.run()
            
            # 释放GPU资源
            self.release_gpu(experiment_id)

            logger.info(f"✅ 攻击实验完成: 检测率 {results.get('detection_rate', 0):.4f}")
            return results

        except Exception as e:
            logger.error(f"❌ 攻击实验失败: {e}")
            try:
                self.release_gpu(experiment_id)
            except:
                pass
            raise
    
    def run_comparison_experiment(self, args) -> Dict[str, Any]:
        """运行方法对比实验"""
        logger.info(f"📊 开始对比实验: {args.methods}")
        
        methods = args.methods.split(',')
        results = {}
        
        for method in methods:
            method = method.strip()
            experiment_id = f"comparison_{method}"
            
            try:
                # 智能分配GPU
                device = self.assign_gpu(experiment_id, args.client_num)
                
                # 根据方法选择基准配置
                if method == 'pol':
                    benchmark = config.benchmark.get_benchmark("PoLFashionMNIST")
                else:
                    benchmark = config.benchmark.get_benchmark("FashionMNIST")
                
                global_args, train_args, algorithm = benchmark.get_args()
                
                # 配置实验参数
                global_args.update({
                    'communication_round': args.rounds,
                    'client_num': args.client_num,
                    'device': device,
                    'enable_pol': (method == 'pol'),
                    'data_folder': os.path.join(self.results_dir, 'data')
                })
                
                logger.info(f"🔄 运行方法: {method}, 设备: {device}")
                
                # 运行实验
                task = Task(global_args=global_args, train_args=train_args, algorithm=algorithm)
                method_results = task.run()
                results[method] = method_results
                
                # 释放GPU资源
                self.release_gpu(experiment_id)

                logger.info(f"✅ 方法 {method} 完成: 准确率 {method_results.get('final_accuracy', 0):.4f}")

            except Exception as e:
                logger.error(f"❌ 方法 {method} 失败: {e}")
                try:
                    self.release_gpu(experiment_id)
                except:
                    pass
                results[method] = {'error': str(e), 'final_accuracy': 0.0}
        
        logger.info("📊 对比实验完成")
        return results
    
    def run_quick_test(self) -> Dict[str, Any]:
        """运行快速测试"""
        logger.info("⚡ 开始快速测试")
        
        # 快速测试配置：小规模、少轮次
        class QuickTestArgs:
            dataset = "FashionMNIST"
            rounds = 2
            client_num = 5
            model = "simpleCNN"  # 修正模型名称，ModelFactory期望小写
            enable_pol = True
        
        return self.run_basic_experiment(QuickTestArgs())

def create_parser():
    """创建简单直观的命令行解析器"""
    parser = argparse.ArgumentParser(
        description="VeryFL-PoL 简单易用的实验入口",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
🔬 VeryFL-PoL 实验框架:
  python run.py                    # 交互式模式，选择要运行的实验
  python run.py experiment1        # 实验1：基础性能对比
  python run.py experiment2        # 实验2：攻击防御效果
  python run.py experiment3        # 实验3：压缩效果分析
  python run.py experiment4        # 实验4：可扩展性测试
  python run.py experiment5        # 实验5：消融实验
  python run.py all                # 运行完整的五个实验板块

📊 结果分析:
  python analyze_results.py        # 分析多次实验结果
  python analyze_results.py --experiment experiment1  # 分析特定实验
  python analyze_results.py --help # 查看分析工具帮助
        """
    )

    # 简化的命令结构 - 不使用子命令，直接用位置参数
    parser.add_argument('mode', nargs='?', default='interactive',
                       choices=['experiment1', 'experiment2', 'experiment3',
                               'experiment4', 'experiment5', 'all', 'interactive'],
                       help='要运行的实验')

    # 可选的高级参数（大多数用户不需要）
    parser.add_argument('--dataset', choices=['FashionMNIST', 'CIFAR10', 'CIFAR100'],
                       help='数据集 (高级用法)')
    parser.add_argument('--clients', type=int, help='客户端数量 (高级用法)')
    parser.add_argument('--rounds', type=int, help='训练轮数 (高级用法)')
    parser.add_argument('--no-pol', action='store_true', help='禁用PoL (高级用法)')

    return parser

def main():
    """主函数 - 简单直观的入口"""
    parser = create_parser()
    args = parser.parse_args()

    # 创建运行器
    runner = VeryFLRunner()

    try:
        # 根据模式执行相应实验
        mode = args.mode

        if mode == 'interactive':
            # 交互式模式
            results = runner.interactive_mode()
        elif mode == 'experiment1':
            # 实验1
            results = runner.run_experiment_1()
        elif mode == 'experiment2':
            # 实验2
            results = runner.run_experiment_2()
        elif mode == 'experiment3':
            # 实验3
            results = runner.run_experiment_3()
        elif mode == 'experiment4':
            # 实验4
            results = runner.run_experiment_4()
        elif mode == 'experiment5':
            # 实验5
            results = runner.run_experiment_5()
        elif mode == 'all':
            # 完整实验框架
            results = runner.run_all_experiments()
        else:
            logger.error(f"❌ 未知模式: {mode}")
            parser.print_help()
            return

        if results:
            logger.info("🎉 实验执行完成！")

    except KeyboardInterrupt:
        print("\n👋 再见！")
    except Exception as e:
        logger.error(f"❌ 实验执行失败: {e}")
        raise

if __name__ == "__main__":
    main()
