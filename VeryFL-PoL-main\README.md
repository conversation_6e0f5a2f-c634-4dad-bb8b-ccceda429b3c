# VeryFL-PoL: 可验证联邦学习框架

## 🎯 项目概述

VeryFL-PoL是一个集成了Proof of Learning (PoL)的联邦学习框架，基于VeryFL构建，提供：

- **🔒 可验证的联邦学习**：基于PoL论文标准的验证机制
- **⚡ 智能验证策略**：多距离度量 + 动态阈值校准
- **🔗 区块链集成**：去中心化的激励和验证
- **📊 全面的实验框架**：SOTA方法对比和性能分析

## 🚀 快速开始

### 🎯 新统一接口（推荐）

```bash
# 1. 快速测试（2轮，5客户端）
python run.py quick-test

# 2. 基础实验
python run.py basic --dataset FashionMNIST --rounds 5 --client-num 10 --enable-pol

# 3. 攻击防御实验
python run.py attack --type model_poison --ratio 0.1

# 4. 方法对比实验
python run.py comparison --methods pol,fedavg,krum

# 5. 批量实验套件
python experiments.py suite --name quick          # 快速测试套件
python experiments.py suite --name performance    # 性能对比套件
python experiments.py suite --name attack_defense # 攻击防御套件

# 6. 网络可扩展性实验
python experiments.py network --clients 10,20,30

# 7. 压缩效果实验
python experiments.py compression --methods all
```

### 📜 传统接口（仍支持）

```bash
# 测试新的PoL改进功能
python test_new_pol_improvements.py

# 运行全面的功能测试
python test_pol_comprehensive_improvements.py

# 运行VeryFL基础测试
python test.py --benchmark FashionMNIST

# 快速实验套件
cd experiments
python enhanced_parallel_runner.py --suite quick

# SOTA方法对比
python sota_comparison.py
```

## 📁 项目结构

```
VeryFL-PoL/
├── run.py                  # 🚀 统一实验入口
├── experiments.py          # 📊 批量实验管理器
├── config.yaml            # ⚙️ 统一配置文件
├── pol/                    # PoL核心模块（我们的改进）
├── client/                 # VeryFL客户端
├── server/                 # VeryFL服务器
├── experiments/            # 实验框架
├── config/                 # 配置系统
├── model/                  # 模型定义
├── dataset/                # 数据集处理
├── chainEnv/              # 区块链环境
├── chainfl/               # 区块链FL集成
├── docs/                  # 文档
└── util/                  # 工具函数
```

## ⚙️ 配置管理

### 统一配置文件 `config.yaml`
```yaml
# 数据集和模型配置
datasets: [FashionMNIST, CIFAR10, CIFAR100]
models: [simpleCNN, resnet18, resnet34]

# 实验规模配置
client_nums: [5, 10, 20]
communication_rounds: [2, 5, 10]

# 预定义实验套件
experiment_suites:
  quick:
    - name: "pol_quick_test"
      dataset: "FashionMNIST"
      rounds: 2
      client_num: 5
      enable_pol: true
```

## 🔧 核心功能

### PoL验证器增强功能
- **多距离度量验证**：L1, L2, L∞, 余弦距离
- **动态阈值校准**：基于模型、硬件、数据集自适应
- **统计测试验证**：Kolmogorov-Smirnov测试
- **智能采样策略**：验证预算Q优化

### VeryFL核心功能（保持不变）
- **多种聚合算法**：FedAvg, Krum, Median等
- **攻击模拟**：Free-rider, Byzantine等
- **防御机制**：多种SOTA防御方法
- **性能评估**：全面的指标和可视化

## 📊 使用示例

```python
# 创建增强的PoL验证器
from pol.pol_verifier import PoLVerifier

verifier = PoLVerifier(
    verification_budget_Q=1,      # 智能验证预算
    enable_multi_metric=True      # 启用多距离度量
)

# 验证PoL证明
result = verifier.verify_proof(
    pol_proof, 
    model_architecture=YourModel,
    dataloader=your_dataloader,
    enable_reproduction=True
)
```

## 📈 性能特点

- **验证效率**：智能采样策略，验证时间 < 0.1秒
- **验证准确性**：多维度验证，大幅提升安全性
- **向后兼容**：完全兼容原VeryFL功能
- **易于集成**：插件式PoL集成，不影响原有功能

## 🤝 贡献

本项目基于VeryFL框架，集成了PoL验证机制。我们保持了VeryFL的所有核心功能，只是添加了可验证性。

## 📄 许可证

遵循原VeryFL项目的许可证。
