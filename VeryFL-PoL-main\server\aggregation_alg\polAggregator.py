"""
支持PoL验证的聚合器
在聚合前验证客户端提交的学习证明
"""

import logging
import torch
from typing import List, Dict, Any, OrderedDict
from copy import deepcopy
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
import multiprocessing
import threading
import time

from server.base.baseAggregator import ServerAggregator
from server.aggregation_alg.fedavg import fedavgAggregator
from client.polClient import PoLClient
from pol.pol_verifier import PoLVerifier
from chainfl.pol_blockchain_client import pol_blockchain_client

logger = logging.getLogger(__name__)

class PoLAggregator(ServerAggregator):
    """支持PoL验证的基础聚合器"""
    
    def __init__(self, model=None, args=None, pol_config=None):
        """
        初始化PoL聚合器

        Args:
            model: 全局模型
            args: 聚合器参数
            pol_config: PoL配置参数
        """
        super().__init__(model, args)

        # PoL配置
        self.pol_config = pol_config or {}
        self.enable_pol_verification = self.pol_config.get('enable_verification', True)
        self.verification_budget_Q = self.pol_config.get('verification_budget_Q', 1)  # 基于PoL论文
        self.require_pol = self.pol_config.get('require_pol', True)

        # 并行验证配置
        self.enable_parallel_verification = self.pol_config.get('enable_parallel_verification', True)
        self.max_verification_workers = self.pol_config.get('max_verification_workers',
                                                          min(4, multiprocessing.cpu_count()))
        self.verification_timeout = self.pol_config.get('verification_timeout', 120)  # 2分钟超时，给重现验证足够时间

        # 并行验证配置
        self.enable_parallel_verification = self.pol_config.get('enable_parallel_verification', True)
        self.max_verification_workers = self.pol_config.get('max_verification_workers',
                                                          min(4, multiprocessing.cpu_count()))
        self.enable_blockchain = self.pol_config.get('enable_blockchain', True)
        self.enable_incentives = self.pol_config.get('enable_incentives', True)

        # 初始化PoL验证器（使用增强功能）
        if self.enable_pol_verification:
            enable_gpu = self.pol_config.get('enable_gpu_acceleration', True)

            # 从pol_config中获取模型配置信息
            model_config = {
                'class_num': self.pol_config.get('class_num', 10),
                'input_channels': self.pol_config.get('input_channels', 1),
                'input_size': self.pol_config.get('input_size', 28)
            }

            self.pol_verifier = PoLVerifier(
                verification_budget_Q=self.verification_budget_Q,
                enable_multi_metric=True,  # 启用多距离度量验证
                enable_gpu_acceleration=enable_gpu,  # GPU加速配置
                enable_parallel_verification=self.enable_parallel_verification,  # 并行验证配置
                max_parallel_workers=self.max_verification_workers,  # 并行工作线程数
                model_config=model_config  # 传递模型配置
            )
            logger.info(f"PoL聚合器初始化完成，已启用PoL验证 (验证预算Q={self.verification_budget_Q})")
        else:
            self.pol_verifier = None
            logger.info("PoL聚合器初始化完成，未启用PoL验证")
        
        # 验证统计
        self.verification_stats = {
            'total_clients': 0,
            'verified_clients': 0,
            'failed_clients': 0,
            'no_pol_clients': 0
        }
    
    def receive_upload(self, client_pool: List[PoLClient]):
        """
        接收客户端上传并进行PoL验证（批量优化版本）

        Args:
            client_pool: 客户端池
        """
        self.model_pool = []
        self.verified_clients = []  # 存储为实例变量

        self.verification_stats['total_clients'] = len(client_pool)

        logger.info(f"🚀 开始批量接收 {len(client_pool)} 个客户端的上传")

        # 第一阶段：收集所有客户端的上传数据
        client_uploads = []
        for client in client_pool:
            try:
                update_data = client.get_model_update_with_proof()
                client_uploads.append({
                    'client': client,
                    'update_data': update_data
                })
            except Exception as e:
                logger.error(f"接收客户端 {client.client_id} 上传时发生错误: {e}")
                self.verification_stats['failed_clients'] += 1

        # 第二阶段：批量验证PoL证明
        verification_results = self._batch_verify_pol_proofs(client_uploads)

        # 第三阶段：处理验证结果
        for client_upload, is_valid in zip(client_uploads, verification_results):
            client = client_upload['client']
            update_data = client_upload['update_data']

            if is_valid:
                self.model_pool.append(update_data['state_dict'])
                self.verified_clients.append(client)
                self.verification_stats['verified_clients'] += 1
                logger.debug(f"客户端 {client.client_id} 验证通过")
            else:
                self.verification_stats['failed_clients'] += 1
                logger.warning(f"客户端 {client.client_id} PoL验证失败，排除其模型更新")

        logger.info(f"🎉 批量PoL验证完成: 总客户端 {self.verification_stats['total_clients']}, "
                   f"验证通过 {self.verification_stats['verified_clients']}, "
                   f"验证失败 {self.verification_stats['failed_clients']}, "
                   f"无PoL {self.verification_stats['no_pol_clients']}")

        return self.verified_clients

    def _batch_verify_pol_proofs(self, client_uploads: List[Dict]) -> List[bool]:
        """批量验证PoL证明 - 真正的并行验证优化"""
        logger.info(f"🔍 开始批量验证 {len(client_uploads)} 个PoL证明 (并行: {self.enable_parallel_verification})")

        if self.enable_parallel_verification and len(client_uploads) > 1:
            return self._parallel_verify_pol_proofs(client_uploads)
        else:
            return self._sequential_verify_pol_proofs(client_uploads)

    def _parallel_verify_pol_proofs(self, client_uploads: List[Dict]) -> List[bool]:
        """并行验证PoL证明"""
        verification_results = [False] * len(client_uploads)

        # 使用线程池进行并行验证
        with ThreadPoolExecutor(max_workers=self.max_verification_workers) as executor:
            # 提交所有验证任务
            future_to_index = {}
            for i, client_upload in enumerate(client_uploads):
                future = executor.submit(self._verify_single_client_pol, client_upload)
                future_to_index[future] = i

            # 收集验证结果
            completed_count = 0
            for future in as_completed(future_to_index, timeout=self.verification_timeout):
                try:
                    index = future_to_index[future]
                    result = future.result()
                    verification_results[index] = result['is_valid']
                    completed_count += 1

                    client_id = client_uploads[index]['client'].client_id
                    if result['is_valid']:
                        logger.debug(f"✅ 客户端 {client_id} 并行PoL验证通过 ({completed_count}/{len(client_uploads)})")
                    else:
                        logger.warning(f"❌ 客户端 {client_id} 并行PoL验证失败: {result.get('error_message', '未知错误')}")

                except Exception as e:
                    index = future_to_index[future]
                    client_id = client_uploads[index]['client'].client_id
                    logger.error(f"❌ 客户端 {client_id} 验证异常: {e}")
                    verification_results[index] = False

        success_count = sum(verification_results)
        logger.info(f"🎉 并行验证完成: {success_count}/{len(client_uploads)} 通过")

        # 异步提交区块链验证
        self._async_submit_blockchain_verifications(client_uploads, verification_results)

        return verification_results

    def _sequential_verify_pol_proofs(self, client_uploads: List[Dict]) -> List[bool]:
        """串行验证PoL证明 (原有逻辑)"""
        verification_results = []

        # 串行进行本地PoL验证
        for client_upload in client_uploads:
            try:
                result = self._verify_single_client_pol(client_upload)
                verification_results.append(result['is_valid'])

                client_id = client_upload['client'].client_id
                if result['is_valid']:
                    logger.debug(f"客户端 {client_id} 本地PoL验证通过")
                else:
                    logger.warning(f"客户端 {client_id} 本地PoL验证失败: {result.get('error_message', '未知错误')}")

                # 准备区块链验证数据

            except Exception as e:
                logger.error(f"客户端 {client_upload['client'].client_id} 验证异常: {e}")
                verification_results.append(False)

        return verification_results

    def _verify_single_client_pol(self, client_upload: Dict) -> Dict[str, Any]:
        """验证单个客户端的PoL证明 - 线程安全版本"""
        client = client_upload['client']
        update_data = client_upload['update_data']

        try:
            pol_proof = update_data['pol_proof']

            # 使用PoL验证器进行验证，传递实际的模型实例而不是类型
            verification_result = self.pol_verifier.verify_proof(
                pol_proof,
                model_architecture=client.model,  # 传递模型实例
                dataloader=getattr(client, 'dataloader', None),
                device=self.args.get('device', 'cpu') if self.args else 'cpu',
                enable_reproduction=True
            )

            return {
                'is_valid': verification_result['is_valid'],
                'error_message': verification_result.get('error_message', ''),
                'client_id': client.client_id
            }

        except Exception as e:
            return {
                'is_valid': False,
                'error_message': str(e),
                'client_id': client.client_id
            }

    def _async_submit_blockchain_verifications(self, client_uploads: List[Dict], verification_results: List[bool]):
        """异步提交区块链验证 - 不阻塞主流程"""
        def blockchain_submit_task():
            try:
                for client_upload, is_valid in zip(client_uploads, verification_results):
                    client = client_upload['client']
                    self._submit_verification_to_blockchain(client, is_valid)
            except Exception as e:
                logger.warning(f"批量区块链提交异常: {e}，但不影响主流程")

        # 在独立线程中执行区块链提交
        thread = threading.Thread(target=blockchain_submit_task, daemon=True)
        thread.start()
        logger.debug("区块链验证提交任务已启动（异步）")

    def _batch_submit_blockchain_verifications(self, verifications: List[Dict]):
        """批量提交区块链验证结果"""
        from chainfl.pol_blockchain_client import pol_blockchain_client

        logger.info(f"📋 开始批量提交 {len(verifications)} 个区块链验证结果")

        # 准备批量验证数据
        batch_verifications = []
        for verification in verifications:
            client = verification['client']
            is_valid = verification['is_valid']

            try:
                # 获取客户端的实际区块链地址
                query_address = getattr(client, 'actual_blockchain_address', None)
                if not query_address:
                    client_account = pol_blockchain_client._get_client_account(client.client_address)
                    query_address = client_account.address

                # 获取PoL记录
                pol_records = pol_blockchain_client.get_pol_records(query_address)

                if pol_records:
                    # 找到最新的未验证记录
                    latest_unverified_index = -1
                    for i in range(len(pol_records) - 1, -1, -1):
                        if not pol_records[i]['is_verified']:
                            latest_unverified_index = i
                            break

                    if latest_unverified_index != -1:
                        batch_verifications.append({
                            'client_address': query_address,
                            'record_index': latest_unverified_index,
                            'is_valid': is_valid,
                            'verifier_address': pol_blockchain_client.accounts[0].address
                        })

            except Exception as e:
                logger.error(f"准备客户端 {client.client_id} 区块链验证数据时发生错误: {e}")

        # 批量提交到区块链
        if batch_verifications:
            results = pol_blockchain_client.batch_verify_pol_proofs(batch_verifications)
            success_count = sum(results)
            logger.info(f"🎉 批量区块链验证提交完成：{success_count}/{len(batch_verifications)} 成功")

    def _verify_client_pol(self, client: PoLClient, update_data: Dict[str, Any]) -> bool:
        """
        验证单个客户端的PoL证明
        
        Args:
            client: 客户端实例
            update_data: 客户端更新数据
            
        Returns:
            验证结果
        """
        if not self.enable_pol_verification:
            return True  # 如果未启用验证，默认通过
        
        # 检查是否有PoL证明
        if not update_data.get('has_pol', False) or update_data.get('pol_proof') is None:
            if self.require_pol:
                logger.warning(f"客户端 {client.client_id} 未提供PoL证明")
                self.verification_stats['no_pol_clients'] += 1
                return False
            else:
                logger.info(f"客户端 {client.client_id} 未提供PoL证明，但不强制要求")
                self.verification_stats['no_pol_clients'] += 1
                return True
        
        try:
            pol_proof = update_data['pol_proof']
            
            # 使用PoL验证器进行验证（启用重现验证以确保真实性）
            # 修复：传递模型实例而不是类型，让验证器自己处理实例化
            verification_result = self.pol_verifier.verify_proof(
                pol_proof,
                model_architecture=client.model,  # 传递模型实例，验证器会处理
                dataloader=getattr(client, 'dataloader', None),  # 传递数据加载器
                device=self.args.get('device', 'cpu') if self.args else 'cpu',
                enable_reproduction=True  # 启用重现验证确保PoL真实性
            )
            
            is_valid = verification_result['is_valid']
            
            if is_valid:
                logger.debug(f"客户端 {client.client_id} PoL验证通过")
            else:
                logger.warning(f"客户端 {client.client_id} PoL验证失败: "
                             f"{verification_result.get('error_message', '未知错误')}")

            # 将验证结果提交到区块链
            if self.enable_blockchain:
                self._submit_verification_to_blockchain(client, is_valid)

            return is_valid
            
        except Exception as e:
            logger.error(f"验证客户端 {client.client_id} PoL时发生错误: {e}")
            return False
    
    def aggregate(self, raw_client_model_or_grad_list: List[OrderedDict] = None) -> OrderedDict:
        """
        聚合经过PoL验证的模型

        Args:
            raw_client_model_or_grad_list: 客户端模型列表

        Returns:
            聚合后的模型
        """
        logger.info(f"🔄 开始模型聚合阶段")

        if raw_client_model_or_grad_list is None:
            raw_client_model_or_grad_list = self.model_pool

        if not raw_client_model_or_grad_list:
            logger.error("❌ 没有有效的客户端模型可供聚合")
            return None

        logger.info(f"📊 准备聚合 {len(raw_client_model_or_grad_list)} 个客户端模型")
        
        # 调用具体的聚合算法
        aggregated_model = self._aggregate_alg(raw_client_model_or_grad_list)
        
        logger.info(f"模型聚合完成，参与聚合的客户端数量: {len(raw_client_model_or_grad_list)}")

        # 分配激励奖励
        if self.enable_incentives and self.enable_blockchain:
            self._distribute_incentives(getattr(self, 'verified_clients', []))

        return aggregated_model
    
    def _aggregate_alg(self, raw_client_model_or_grad_list: List[OrderedDict]) -> OrderedDict:
        """
        具体的聚合算法（默认使用FedAvg）
        子类可以重写此方法实现不同的聚合策略
        """
        # 使用FedAvg算法进行聚合
        return self._fedavg_aggregate(raw_client_model_or_grad_list)
    
    def _fedavg_aggregate(self, model_list: List[OrderedDict]) -> OrderedDict:
        """FedAvg聚合算法"""
        if not model_list:
            return None

        # 初始化聚合结果
        aggregated_model = deepcopy(model_list[0])
        
        # 对每个参数进行平均
        for key in aggregated_model.keys():
            # 将第一个模型的参数作为基础
            aggregated_model[key] = model_list[0][key].clone()

            # 累加其他模型的参数
            for i in range(1, len(model_list)):
                aggregated_model[key] += model_list[i][key]

            # 计算平均值
            aggregated_model[key] = aggregated_model[key] / len(model_list)

        # 输出聚合后的模型权重统计信息
        self._log_aggregated_model_stats(aggregated_model)

        return aggregated_model
    
    def _log_aggregated_model_stats(self, aggregated_model: OrderedDict):
        """输出聚合后模型的权重统计信息"""
        import torch

        logger.info("🔄 聚合后模型权重统计:")
        total_params = 0

        for layer_name, params in aggregated_model.items():
            if isinstance(params, torch.Tensor):
                param_count = params.numel()
                param_mean = params.mean().item()
                param_std = params.std().item()
                param_min = params.min().item()
                param_max = params.max().item()

                total_params += param_count

                logger.info(f"  📊 {layer_name}:")
                logger.info(f"    - 参数数量: {param_count:,}")
                logger.info(f"    - 均值: {param_mean:.6f}")
                logger.info(f"    - 标准差: {param_std:.6f}")
                logger.info(f"    - 范围: [{param_min:.6f}, {param_max:.6f}]")

        logger.info(f"  🎯 总参数数量: {total_params:,}")
        logger.info("=" * 50)

    def _on_before_aggregation(self, raw_client_model_or_grad_list: List[OrderedDict]) -> List[OrderedDict]:
        """聚合前的预处理"""
        # 可以在这里添加额外的预处理逻辑
        return raw_client_model_or_grad_list

    def test(self):
        """测试方法（继承自基类的抽象方法）"""
        pass
    
    def get_verification_stats(self) -> Dict[str, Any]:
        """获取验证统计信息"""
        stats = self.verification_stats.copy()
        if stats['total_clients'] > 0:
            stats['verification_rate'] = stats['verified_clients'] / stats['total_clients']
            stats['failure_rate'] = stats['failed_clients'] / stats['total_clients']
        else:
            stats['verification_rate'] = 0.0
            stats['failure_rate'] = 0.0
        
        return stats
    
    def _submit_verification_to_blockchain(self, client: PoLClient, is_valid: bool):
        """将验证结果提交到区块链 - 添加超时和异步处理"""
        import threading
        import time

        def blockchain_submit_task():
            """区块链提交任务 - 在单独线程中执行"""
            try:
                # 获取客户端最新的PoL记录索引
                # 使用客户端实际的区块链地址查询PoL记录
                from chainfl.pol_blockchain_client import pol_blockchain_client

                # 获取客户端实际使用的区块链地址
                if hasattr(client, 'actual_blockchain_address') and client.actual_blockchain_address:
                    query_address = client.actual_blockchain_address
                    logger.debug(f"使用客户端实际区块链地址: {query_address}")
                else:
                    # 如果没有实际地址，通过客户端地址获取对应的区块链账户地址
                    client_account = pol_blockchain_client._get_client_account(client.client_address)
                    query_address = client_account.address
                    logger.debug(f"通过客户端地址 {client.client_address} 获取区块链地址: {query_address}")

                # 添加超时控制的区块链查询
                start_time = time.time()
                timeout = 30  # 30秒超时

                pol_records = None
                try:
                    pol_records = pol_blockchain_client.get_pol_records(query_address)
                    if time.time() - start_time > timeout:
                        logger.warning(f"客户端 {client.client_id} 区块链查询超时，跳过区块链提交")
                        return
                except Exception as e:
                    logger.warning(f"客户端 {client.client_id} 区块链查询失败: {e}，跳过区块链提交")
                    return

                logger.info(f"查询客户端 {client.client_id} (地址: {query_address[-8:]}) 的PoL记录，找到 {len(pol_records) if pol_records else 0} 条")

                if not pol_records:
                    logger.warning(f"客户端 {client.client_id} 没有PoL记录，跳过区块链提交")
                    return

                # 找到最新的未验证记录（从后往前找）
                latest_unverified_index = -1
                for i in range(len(pol_records) - 1, -1, -1):
                    if not pol_records[i]['is_verified']:
                        latest_unverified_index = i
                        break

                if latest_unverified_index == -1:
                    logger.warning(f"客户端 {client.client_id} 没有未验证的PoL记录，跳过区块链提交")
                    return

                # 检查总超时时间
                if time.time() - start_time > timeout:
                    logger.warning(f"客户端 {client.client_id} 区块链操作总时间超时，跳过提交")
                    return

                # 提交验证结果 - 添加超时控制
                try:
                    success = pol_blockchain_client.verify_pol_proof(
                        client_address=query_address,  # 使用实际的区块链地址
                        record_index=latest_unverified_index,
                        is_valid=is_valid,
                        verifier_address=pol_blockchain_client.accounts[0].address  # 使用服务器账户地址
                    )

                    if success:
                        logger.info(f"🔗 客户端 {client.client_id} 验证结果已提交到区块链")
                        logger.info(f"   - 验证结果: {'✅ 有效' if is_valid else '❌ 无效'}")
                        logger.info(f"   - 记录索引: {latest_unverified_index}")
                    else:
                        logger.error(f"❌ 客户端 {client.client_id} 验证结果提交失败")

                except Exception as e:
                    logger.warning(f"客户端 {client.client_id} 区块链提交异常: {e}，但不影响主流程")

            except Exception as e:
                logger.warning(f"客户端 {client.client_id} 区块链提交任务异常: {e}，但不影响主流程")

        # 异步执行区块链提交，避免阻塞主流程
        try:
            thread = threading.Thread(target=blockchain_submit_task, daemon=True)
            thread.start()
            logger.debug(f"客户端 {client.client_id} 区块链提交任务已启动（异步）")
        except Exception as e:
            logger.warning(f"客户端 {client.client_id} 无法启动区块链提交线程: {e}，跳过区块链提交")

        except Exception as e:
            logger.error(f"提交验证结果到区块链时发生错误: {e}")

    def _distribute_incentives(self, verified_clients: List[PoLClient]):
        """分配激励奖励"""
        try:
            if not verified_clients:
                return

            # 计算奖励分配
            base_reward = 100  # 基础奖励
            client_rewards = {}

            for client in verified_clients:
                # 基于信誉分数计算奖励
                # 使用实际的区块链地址
                if hasattr(client, 'actual_blockchain_address') and client.actual_blockchain_address:
                    client_blockchain_address = client.actual_blockchain_address
                else:
                    client_account = pol_blockchain_client._get_client_account(client.client_address)
                    client_blockchain_address = client_account.address

                reputation = pol_blockchain_client.get_client_reputation(client_blockchain_address)

                # 修复奖励计算逻辑：新客户端给予基础奖励，有信誉的客户端基于信誉计算
                if reputation.reputation_score == 0:
                    # 新客户端或无信誉记录的客户端给予基础奖励
                    reward = 10  # 基础奖励
                else:
                    # 有信誉记录的客户端基于信誉分数计算
                    reputation_multiplier = max(0.05, reputation.reputation_score / 1000.0)  # 最低5%
                    reward = int(base_reward * reputation_multiplier)

                if reward > 0:
                    client_rewards[client_blockchain_address] = reward

            # 分配奖励
            if client_rewards:
                success = pol_blockchain_client.distribute_rewards(client_rewards)
                if success:
                    total_rewards = sum(client_rewards.values())
                    logger.info(f"💰 激励奖励分配完成，总奖励: {total_rewards}")
                    for client_addr, reward in client_rewards.items():
                        logger.info(f"   - 客户端 {client_addr[-8:]}: {reward} 奖励")
                else:
                    logger.error("❌ 激励奖励分配失败")

        except Exception as e:
            logger.error(f"分配激励奖励时发生错误: {e}")

    def reset_verification_stats(self):
        """重置验证统计"""
        self.verification_stats = {
            'total_clients': 0,
            'verified_clients': 0,
            'failed_clients': 0,
            'no_pol_clients': 0
        }


class PoLFedAvgAggregator(PoLAggregator):
    """支持PoL验证的FedAvg聚合器"""
    
    def __init__(self, model=None, args=None, pol_config=None):
        super().__init__(model, args, pol_config)
        logger.info("PoL FedAvg聚合器初始化完成")
    
    def _aggregate_alg(self, raw_client_model_or_grad_list: List[OrderedDict]) -> OrderedDict:
        """使用FedAvg算法进行聚合"""
        return self._fedavg_aggregate(raw_client_model_or_grad_list)


class PoLWeightedAggregator(PoLAggregator):
    """支持PoL验证的加权聚合器"""
    
    def __init__(self, model=None, args=None, pol_config=None):
        super().__init__(model, args, pol_config)
        self.client_weights = {}  # 存储客户端权重
        logger.info("PoL加权聚合器初始化完成")
    
    def set_client_weights(self, client_weights: Dict[str, float]):
        """设置客户端权重"""
        self.client_weights = client_weights
    
    def _aggregate_alg(self, raw_client_model_or_grad_list: List[OrderedDict]) -> OrderedDict:
        """使用加权平均进行聚合"""
        if not raw_client_model_or_grad_list:
            return None
        
        # 如果没有设置权重，使用等权重
        if not self.client_weights:
            return self._fedavg_aggregate(raw_client_model_or_grad_list)
        
        # 加权聚合
        aggregated_model = deepcopy(raw_client_model_or_grad_list[0])
        total_weight = sum(self.client_weights.values())
        
        for key in aggregated_model.keys():
            aggregated_model[key] = torch.zeros_like(aggregated_model[key])
            
            for i, model_state in enumerate(raw_client_model_or_grad_list):
                client_id = f"client_{i}"  # 简化的客户端ID
                weight = self.client_weights.get(client_id, 1.0) / total_weight
                aggregated_model[key] += weight * model_state[key]
        
        return aggregated_model
