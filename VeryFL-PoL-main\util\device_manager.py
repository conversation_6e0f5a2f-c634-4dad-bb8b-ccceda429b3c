"""
智能设备管理器
自动检测并配置最佳的计算设备（CUDA/CPU）
"""

import torch
import logging
import warnings
import os

logger = logging.getLogger(__name__)

class DeviceManager:
    """智能设备管理器"""
    
    def __init__(self):
        self._device = None
        self._device_type = None
        self._cuda_available = torch.cuda.is_available()
        self._device_count = torch.cuda.device_count() if self._cuda_available else 0
        
        # 初始化设备
        self._initialize_device()
    
    def _initialize_device(self):
        """初始化最佳设备"""
        if self._cuda_available and self._device_count > 0:
            self._device = torch.device('cuda')
            self._device_type = 'cuda'
            logger.info(f"✅ 检测到CUDA设备，使用GPU加速")
            logger.info(f"📊 CUDA设备数量: {self._device_count}")
            logger.info(f"🔧 当前设备: {torch.cuda.get_device_name(0)}")
        else:
            self._device = torch.device('cpu')
            self._device_type = 'cpu'
            logger.info(f"⚠️ 未检测到CUDA设备，使用CPU计算")
            logger.info(f"💡 如需GPU加速，请安装CUDA版本的PyTorch")
    
    def get_device(self):
        """获取当前设备"""
        return self._device
    
    def get_device_type(self):
        """获取设备类型字符串"""
        return self._device_type
    
    def is_cuda_available(self):
        """检查CUDA是否可用"""
        return self._cuda_available
    
    def get_device_info(self):
        """获取设备详细信息"""
        info = {
            'device': str(self._device),
            'device_type': self._device_type,
            'cuda_available': self._cuda_available,
            'device_count': self._device_count,
            'pytorch_version': torch.__version__
        }
        
        if self._cuda_available:
            info['cuda_version'] = torch.version.cuda
            info['device_name'] = torch.cuda.get_device_name(0)
            info['memory_allocated'] = torch.cuda.memory_allocated(0)
            info['memory_cached'] = torch.cuda.memory_reserved(0)
        
        return info
    
    def optimize_for_device(self, model):
        """为当前设备优化模型"""
        model = model.to(self._device)
        
        if self._cuda_available:
            # CUDA优化
            torch.backends.cudnn.benchmark = True
            logger.info("🚀 启用CUDA优化")
        else:
            # CPU优化
            torch.set_num_threads(torch.get_num_threads())
            logger.info(f"🔧 CPU线程数: {torch.get_num_threads()}")
        
        return model
    
    def get_optimal_device_config(self):
        """获取最优设备配置，支持CPU降级"""
        if self._cuda_available and self._device_count > 0:
            return {
                'device': 'cuda',
                'device_type': 'cuda',
                'gpu_count': self._device_count,
                'parallel_workers': min(self._device_count * 2, 8),
                'batch_size_multiplier': self._device_count,
                'memory_efficient': False,
                'recommended_batch_size': 128 * self._device_count,
                'max_parallel_experiments': self._device_count * 2
            }
        else:
            return {
                'device': 'cpu',
                'device_type': 'cpu',
                'gpu_count': 0,
                'parallel_workers': min(torch.get_num_threads() // 2, 4),  # CPU模式下减少并行度
                'batch_size_multiplier': 1,
                'memory_efficient': True,
                'recommended_batch_size': 32,  # CPU模式下使用较小批次
                'max_parallel_experiments': 2  # CPU模式下限制并行实验数
            }

    def auto_configure_args(self, args_dict):
        """自动配置参数字典中的设备设置"""
        if isinstance(args_dict, dict):
            # 获取最优配置
            optimal_config = self.get_optimal_device_config()

            # 智能设置设备
            original_device = args_dict.get('device', 'auto')

            # 如果设置为'auto'或者原本就是需要自动检测的，则使用检测到的设备
            if original_device in ['auto', 'cuda', 'cpu']:
                args_dict['device'] = optimal_config['device_type']

                if original_device == 'auto':
                    logger.info(f"🔄 自动检测设备: {optimal_config['device_type']}")
                elif original_device != optimal_config['device_type']:
                    logger.info(f"🔄 设备配置已从 '{original_device}' 自动调整为 '{optimal_config['device_type']}'")

            # 根据设备类型调整其他参数
            if optimal_config['device_type'] == 'cpu':
                # CPU优化：调整批次大小、并行度等
                if 'batch_size' in args_dict and args_dict['batch_size'] > optimal_config['recommended_batch_size']:
                    original_batch_size = args_dict['batch_size']
                    args_dict['batch_size'] = optimal_config['recommended_batch_size']
                    logger.info(f"🔄 CPU模式下批次大小从 {original_batch_size} 调整为 {args_dict['batch_size']}")

                # 调整学习率（CPU模式下可能需要不同的学习率）
                if 'lr' in args_dict and args_dict['lr'] > 0.01:
                    original_lr = args_dict['lr']
                    args_dict['lr'] = min(0.01, args_dict['lr'])
                    logger.info(f"🔄 CPU模式下学习率从 {original_lr} 调整为 {args_dict['lr']}")

                # 设置内存高效模式
                args_dict['memory_efficient'] = True
                logger.info("🔄 CPU模式下启用内存高效模式")

            else:
                # GPU模式优化
                if 'batch_size' in args_dict:
                    # 根据GPU数量调整批次大小
                    suggested_batch_size = args_dict['batch_size'] * optimal_config['batch_size_multiplier']
                    if suggested_batch_size != args_dict['batch_size']:
                        original_batch_size = args_dict['batch_size']
                        args_dict['batch_size'] = suggested_batch_size
                        logger.info(f"🔄 GPU模式下批次大小从 {original_batch_size} 调整为 {args_dict['batch_size']}")

        return args_dict
    
    def print_device_summary(self):
        """打印设备摘要信息"""
        print("\n" + "="*60)
        print("🖥️  设备配置摘要")
        print("="*60)
        print(f"📱 当前设备: {self._device}")
        print(f"🔧 设备类型: {self._device_type}")
        print(f"🚀 CUDA可用: {'是' if self._cuda_available else '否'}")
        print(f"📊 PyTorch版本: {torch.__version__}")
        
        if self._cuda_available:
            print(f"🎮 GPU设备数: {self._device_count}")
            print(f"🏷️  GPU名称: {torch.cuda.get_device_name(0)}")
            print(f"💾 CUDA版本: {torch.version.cuda}")
        else:
            print(f"🧠 CPU线程数: {torch.get_num_threads()}")
            print(f"💡 提示: 安装CUDA版本PyTorch可获得更好性能")
        
        print("="*60 + "\n")

# 创建全局设备管理器实例
device_manager = DeviceManager()

# 便捷函数
def get_device():
    """获取当前最佳设备"""
    return device_manager.get_device()

def get_device_type():
    """获取设备类型"""
    return device_manager.get_device_type()

def get_optimal_device_config():
    """获取最优设备配置"""
    return device_manager.get_optimal_device_config()

def auto_configure_device(args_dict):
    """自动配置设备参数"""
    return device_manager.auto_configure_args(args_dict)

def optimize_model_for_device(model):
    """为当前设备优化模型"""
    return device_manager.optimize_for_device(model)

def is_gpu_available():
    """检查GPU是否可用"""
    return device_manager.is_cuda_available()

def get_device_info():
    """获取设备详细信息"""
    return device_manager.get_device_info()

def print_device_summary():
    """打印设备摘要"""
    return device_manager.print_device_summary()
